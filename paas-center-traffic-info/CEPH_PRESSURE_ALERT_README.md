# Ceph压力数据上报与预警功能说明

## 功能概述

本次更新对ceph数据上报逻辑进行了以下调整：

1. **Redis缓存实时压力数据**：上报的ceph压力数据按集群缓存到Redis中，缓存不设置过期时间
2. **实时压力查询接口**：提供按集群编号查询实时压力的接口
3. **钉钉机器人预警**：当压力值超过80%时，自动发送钉钉预警消息
4. **美化预警消息**：预警消息包含emoji表情和颜色处理，美观且具有预警效果

## 新增接口

### 1. 根据集群编号查询实时压力数据
```
GET /traffic-info/open/traffic/edge/ceph/realtime/{clusterCode}
```

**参数说明：**
- `clusterCode`: 集群编号（路径参数）

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "clusterCode": "001",
    "currentPressure": 85.5,
    "lastUpdateTime": "2025-07-29 15:30:25",
    "pressureStatus": "注意",
    "isAlert": true
  }
}
```

### 2. 查询所有集群的实时压力数据
```
GET /traffic-info/open/traffic/edge/ceph/realtime/all
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "clusterCode": "001",
      "currentPressure": 85.5,
      "lastUpdateTime": "2025-07-29 15:30:25",
      "pressureStatus": "注意",
      "isAlert": true
    },
    {
      "clusterCode": "002",
      "currentPressure": 65.2,
      "lastUpdateTime": "2025-07-29 15:29:18",
      "pressureStatus": "正常",
      "isAlert": false
    }
  ]
}
```

### 3. 测试钉钉消息发送
```
POST /traffic-info/open/traffic/edge/ceph/dingtalk/test
```

## 配置说明

### 钉钉机器人配置

在配置文件中添加以下配置：

```yaml
dingtalk:
  webhook:
    url: https://oapi.dingtalk.com/robot/send?access_token=YOUR_ACCESS_TOKEN
  alert:
    enabled: true
```

### 配置步骤

1. **创建钉钉机器人**
   - 在钉钉群聊中添加自定义机器人
   - 选择"自定义"机器人类型
   - 设置机器人名称和头像
   - 获取Webhook地址

2. **配置访问令牌**
   - 将获取的Webhook地址中的access_token替换到配置文件中
   - 确保机器人有发送消息的权限

3. **测试配置**
   - 调用测试接口验证钉钉消息发送是否正常
   - 检查群聊中是否收到测试消息

## 预警规则

### 压力阈值分级
- **良好**: < 60%
- **正常**: 60% - 79%
- **注意**: 80% - 89% （触发预警）
- **警告**: 90% - 94%
- **危险**: ≥ 95%

### 预警触发条件
- 当ceph压力值 > 80% 时自动触发钉钉预警
- 预警消息包含集群信息、当前压力值、预警级别等详细信息
- 提供处理建议和联系方式

## 数据流程

1. **数据上报**：外部系统通过现有接口上报ceph压力数据
2. **数据存储**：数据保存到数据库的同时缓存到Redis
3. **实时查询**：通过新接口从Redis获取最新的压力数据
4. **预警检查**：每次数据上报时检查是否超过预警阈值
5. **消息发送**：超过阈值时异步发送钉钉预警消息

## Redis缓存说明

### 缓存Key格式
```
ceph_cluster_pressure:{clusterCode}
```

### 缓存数据格式
```json
{
  "pressure": 85.5,
  "updateTime": "2025-07-29 15:30:25"
}
```

### 缓存特点
- **无过期时间**：缓存数据不会自动过期，始终保持最新状态
- **实时更新**：每次数据上报都会更新对应集群的缓存数据
- **高性能查询**：实时查询直接从Redis获取，响应速度快

## 注意事项

1. **钉钉机器人限制**
   - 每个机器人每分钟最多发送20条消息
   - 建议在高频预警场景下增加防重复发送机制

2. **Redis连接**
   - 确保Redis服务正常运行
   - 检查Redis连接配置是否正确

3. **网络连通性**
   - 确保服务器能够访问钉钉API
   - 检查防火墙和网络代理设置

4. **日志监控**
   - 关注预警发送的日志信息
   - 监控Redis缓存的读写操作

## 故障排查

### 常见问题

1. **钉钉消息发送失败**
   - 检查Webhook URL是否正确
   - 验证access_token是否有效
   - 确认网络连通性

2. **实时数据查询为空**
   - 检查Redis连接状态
   - 确认数据上报是否正常
   - 验证缓存Key格式

3. **预警未触发**
   - 检查压力值是否真的超过80%
   - 确认钉钉预警功能是否启用
   - 查看相关错误日志

### 日志关键字
- `缓存集群.*实时压力数据到Redis`
- `集群.*压力超过预警阈值`
- `钉钉消息发送成功`
- `发送钉钉预警消息异常`
