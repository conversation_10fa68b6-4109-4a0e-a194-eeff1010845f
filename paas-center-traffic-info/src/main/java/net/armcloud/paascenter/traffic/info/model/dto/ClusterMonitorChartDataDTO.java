package net.armcloud.paascenter.traffic.info.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 集群监控折线图数据DTO（替代Map结构）
 * 
 * <AUTHOR>
 */
@Data
public class ClusterMonitorChartDataDTO {

    /**
     * 集群编码
     */
    private String clusterCode;

    /**
     * 集群名称
     */
    private String clusterName;

    /**
     * 时间点
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date timePoint;

    /**
     * 平均值
     */
    private Double avgValue;

    /**
     * 最大值
     */
    private Double maxValue;

    /**
     * 最小值
     */
    private Double minValue;

    /**
     * 数据点数量
     */
    private Integer dataCount;

    /**
     * 指标单位
     */
    private String metricUnit;

    public ClusterMonitorChartDataDTO() {}

    public ClusterMonitorChartDataDTO(String clusterCode, Date timePoint,
                                     Double avgValue, Double maxValue, Double minValue,
                                     Integer dataCount, String metricUnit) {
        this.clusterCode = clusterCode;
        this.timePoint = timePoint;
        this.avgValue = avgValue;
        this.maxValue = maxValue;
        this.minValue = minValue;
        this.dataCount = dataCount;
        this.metricUnit = metricUnit;
    }
}
