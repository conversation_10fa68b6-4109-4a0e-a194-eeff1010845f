package net.armcloud.paascenter.traffic.info.service;

import net.armcloud.paascenter.traffic.info.internal.dto.CephPressureReportDTO;
import net.armcloud.paascenter.traffic.info.model.dto.CephPressureChartDTO;
import net.armcloud.paascenter.traffic.info.model.dto.CephPressureQueryDTO;
import net.armcloud.paascenter.traffic.info.model.dto.CephClusterPressureDTO;

import java.util.List;

/**
 * Ceph压力数据服务接口
 * 
 * <AUTHOR>
 */
public interface ICephPressureDataService {

    /**
     * 保存Ceph压力上报数据
     * 
     * @param reportDTO 上报数据
     * @return 是否保存成功
     */
    Boolean saveCephPressureData(CephPressureReportDTO reportDTO);

    /**
     * 查询折线图数据
     * 
     * @param queryDTO 查询条件
     * @return 折线图数据列表
     */
    List<CephPressureChartDTO> getChartData(CephPressureQueryDTO queryDTO);

    /**
     * 清理过期数据
     *
     * @param retentionDays 保留天数
     * @return 清理的数据条数
     */
    int cleanExpiredData(int retentionDays);

    /**
     * 根据集群编号查询实时压力数据
     *
     * @param clusterCode 集群编号
     * @return 实时压力数据
     */
    CephClusterPressureDTO getRealTimePressure(String clusterCode);
}
