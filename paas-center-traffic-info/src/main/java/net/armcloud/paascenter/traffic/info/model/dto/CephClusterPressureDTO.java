package net.armcloud.paascenter.traffic.info.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * Ceph集群实时压力数据DTO
 * 
 * <AUTHOR>
 */
@Data
public class CephClusterPressureDTO {

    /**
     * 集群编码
     */
    private String clusterCode;

    /**
     * 当前压力值（百分比）
     */
    private Double currentPressure;

    /**
     * 最后更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdateTime;

    /**
     * 压力状态描述
     */
    private String pressureStatus;

    /**
     * 是否超过预警阈值
     */
    private Boolean isAlert;

    public CephClusterPressureDTO() {}

    public CephClusterPressureDTO(String clusterCode, Double currentPressure, Date lastUpdateTime) {
        this.clusterCode = clusterCode;
        this.currentPressure = currentPressure;
        this.lastUpdateTime = lastUpdateTime;
        this.isAlert = currentPressure != null && currentPressure > 80.0;
        this.pressureStatus = generatePressureStatus(currentPressure);
    }

    /**
     * 根据压力值生成状态描述
     */
    private String generatePressureStatus(Double pressure) {
        if (pressure == null) {
            return "未知";
        }
        
        if (pressure >= 95) {
            return "危险";
        } else if (pressure >= 90) {
            return "警告";
        } else if (pressure >= 80) {
            return "注意";
        } else if (pressure >= 60) {
            return "正常";
        } else {
            return "良好";
        }
    }
}
