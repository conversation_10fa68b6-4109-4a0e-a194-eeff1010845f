package net.armcloud.paascenter.traffic.info.service;

import com.alibaba.fastjson.JSONObject;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 钉钉预警消息服务
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class DingTalkAlertService {

    @Resource
    private RestTemplate restTemplate;

    @Value("${dingtalk.webhook.url:https://oapi.dingtalk.com/robot/send?access_token=e7c385767be13576698021476e52a089b293235a524b7bd731678b6368e62420}")
    private String dingTalkWebhookUrl;

    @Value("${dingtalk.alert.enabled:true}")
    private boolean alertEnabled;

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 钉钉消息发送线程池
     */
    private ExecutorService dingTalkExecutor;

    @PostConstruct
    public void init() {
        // 初始化钉钉消息发送线程池
        dingTalkExecutor = new ThreadPoolExecutor(
                2, // 核心线程数
                5, // 最大线程数
                60L, TimeUnit.SECONDS, // 线程空闲时间
                new LinkedBlockingQueue<>(100), // 队列容量
                r -> {
                    Thread t = new Thread(r, "dingtalk-alert-" + System.currentTimeMillis());
                    t.setDaemon(true);
                    return t;
                },
                new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略：调用者执行
        );
        log.info("钉钉预警消息发送线程池初始化完成");
    }

    @PreDestroy
    public void destroy() {
        if (dingTalkExecutor != null && !dingTalkExecutor.isShutdown()) {
            dingTalkExecutor.shutdown();
            try {
                if (!dingTalkExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    dingTalkExecutor.shutdownNow();
                }
                log.info("钉钉预警消息发送线程池已关闭");
            } catch (InterruptedException e) {
                dingTalkExecutor.shutdownNow();
                Thread.currentThread().interrupt();
                log.warn("钉钉预警消息发送线程池关闭被中断", e);
            }
        }
    }

    /**
     * 发送Ceph压力预警消息（异步）
     *
     * @param clusterCode 集群编号
     * @param pressure 压力值
     */
    public void sendCephPressureAlert(String clusterCode, Double pressure) {
        if (!alertEnabled) {
            log.info("钉钉预警功能已关闭，跳过发送预警消息");
            return;
        }

        if (dingTalkWebhookUrl == null || dingTalkWebhookUrl.trim().isEmpty()) {
            log.warn("钉钉Webhook URL未配置，无法发送预警消息");
            return;
        }

        // 使用线程池异步发送消息
        dingTalkExecutor.execute(() -> {
            try {
                String message = buildCephPressureAlertMessage(clusterCode, pressure);
                sendDingTalkMessageWithSdk(message);
                log.info("Ceph压力预警消息发送成功: clusterCode={}, pressure={}%", clusterCode, pressure);
            } catch (Exception e) {
                log.error("发送Ceph压力预警消息失败: clusterCode={}, pressure={}%", clusterCode, pressure, e);
            }
        });
    }

    /**
     * 构建Ceph压力预警消息内容
     */
    private String buildCephPressureAlertMessage(String clusterCode, Double pressure) {
        StringBuilder message = new StringBuilder();
        
        // 添加警告emoji和标题
        message.append("🚨 **Ceph集群压力预警** 🚨\n\n");
        
        // 添加详细信息
        message.append("📊 **集群信息**\n");
        message.append("- 集群编号: `").append(clusterCode).append("`\n");
        // 添加状态指示
        if (pressure >= 95) {
            message.append("- 当前压力: <font color=\"#FF0000\">**").append(String.format("%.2f", pressure)).append("%**</font>\n");
        } else if (pressure >= 90) {
            message.append("- 当前压力: <font color=\"#FF8C00\">**").append(String.format("%.2f", pressure)).append("%**</font>\n");
        } else {
            message.append("- 当前压力: <font color=\"#FFD700\">**").append(String.format("%.2f", pressure)).append("%**</font>\n");
        }
        message.append("- 预警阈值: `80.00%`\n");
        message.append("- 预警时间: `").append(DATE_FORMAT.format(new Date())).append("`\n\n");
        
        // 添加状态指示
        if (pressure >= 95) {
            message.append("🔴 **级别**: <font color=\"#FF0000\">**危险危险危险**</font>\n");
        } else if (pressure >= 90) {
            message.append("🟠 **级别**: <font color=\"#FF8C00\">**警告警告警告**</font>\n");
        } else {
            message.append("🟡 **级别**: <font color=\"#FFD700\">**还行还行还行**</font>\n");
        }

        return message.toString();
    }

    /**
     * 使用钉钉SDK发送消息
     */
    private void sendDingTalkMessageWithSdk(String message) {
        try {
            // 创建钉钉客户端
            DingTalkClient client = new DefaultDingTalkClient(dingTalkWebhookUrl);

            // 创建请求对象
            OapiRobotSendRequest request = new OapiRobotSendRequest();
            request.setMsgtype("markdown");

            // 设置markdown消息内容
            OapiRobotSendRequest.Markdown markdown = new OapiRobotSendRequest.Markdown();
            markdown.setTitle("Ceph集群压力预警");
            markdown.setText(message);
            request.setMarkdown(markdown);

            // 设置@相关人员（可选）
            OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
            at.setIsAtAll(false);
            request.setAt(at);

            // 发送消息
            OapiRobotSendResponse response = client.execute(request);

            if (response.isSuccess()) {
                log.info("钉钉消息发送成功: {}", response.getBody());
            } else {
                log.error("钉钉消息发送失败，错误码: {}, 错误信息: {}", response.getErrorCode(), response.getErrmsg());
                // 如果SDK发送失败，尝试使用原有方式发送
                log.info("尝试使用原有方式发送钉钉消息");
                sendDingTalkMessage(message);
            }
        } catch (Exception e) {
            log.error("使用SDK发送钉钉消息异常，尝试使用原有方式发送", e);
            // 如果SDK发送异常，使用原有方式发送
            try {
                sendDingTalkMessage(message);
            } catch (Exception fallbackException) {
                log.error("原有方式发送钉钉消息也失败", fallbackException);
                throw fallbackException;
            }
        }
    }

    /**
     * 发送钉钉消息（原有方式，作为备用）
     */
    private void sendDingTalkMessage(String message) {
        try {
            // 构建钉钉消息体
            Map<String, Object> messageBody = new HashMap<>();
            messageBody.put("msgtype", "markdown");

            Map<String, Object> markdown = new HashMap<>();
            markdown.put("title", "Ceph集群压力预警");
            markdown.put("text", message);
            messageBody.put("markdown", markdown);

            // 设置@所有人（可选）
            Map<String, Object> at = new HashMap<>();
            at.put("isAtAll", false);
            messageBody.put("at", at);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 发送请求
            HttpEntity<String> request = new HttpEntity<>(JSONObject.toJSONString(messageBody), headers);
            ResponseEntity<String> response = restTemplate.postForEntity(dingTalkWebhookUrl, request, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("钉钉消息发送成功: {}", response.getBody());
            } else {
                log.error("钉钉消息发送失败，状态码: {}, 响应: {}", response.getStatusCode(), response.getBody());
            }
        } catch (Exception e) {
            log.error("发送钉钉消息异常", e);
            throw e;
        }
    }

}
