# Ceph集群上报逻辑优化总结

## 修改概述

本次修改主要针对ceph集群上报逻辑中的两个问题进行了优化：

1. **线程池优化**：将发送预警消息时直接使用 `new Thread` 的方式修改为使用线程池
2. **钉钉SDK升级**：将钉钉机器人消息发送方式从直接使用 `RestTemplate` 修改为使用 `alibaba-dingtalk-service-sdk`

## 详细修改内容

### 1. DingTalkAlertService 类修改

#### 1.1 添加依赖导入
```java
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
```

#### 1.2 添加线程池配置
```java
/**
 * 钉钉消息发送线程池
 */
private ExecutorService dingTalkExecutor;

@PostConstruct
public void init() {
    // 初始化钉钉消息发送线程池
    dingTalkExecutor = new ThreadPoolExecutor(
            2, // 核心线程数
            5, // 最大线程数
            60L, TimeUnit.SECONDS, // 线程空闲时间
            new LinkedBlockingQueue<>(100), // 队列容量
            r -> {
                Thread t = new Thread(r, "dingtalk-alert-" + System.currentTimeMillis());
                t.setDaemon(true);
                return t;
            },
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略：调用者执行
    );
    log.info("钉钉预警消息发送线程池初始化完成");
}

@PreDestroy
public void destroy() {
    if (dingTalkExecutor != null && !dingTalkExecutor.isShutdown()) {
        dingTalkExecutor.shutdown();
        try {
            if (!dingTalkExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                dingTalkExecutor.shutdownNow();
            }
            log.info("钉钉预警消息发送线程池已关闭");
        } catch (InterruptedException e) {
            dingTalkExecutor.shutdownNow();
            Thread.currentThread().interrupt();
            log.warn("钉钉预警消息发送线程池关闭被中断", e);
        }
    }
}
```

#### 1.3 修改消息发送方法
```java
/**
 * 发送Ceph压力预警消息（异步）
 */
public void sendCephPressureAlert(String clusterCode, Double pressure) {
    if (!alertEnabled) {
        log.info("钉钉预警功能已关闭，跳过发送预警消息");
        return;
    }

    if (dingTalkWebhookUrl == null || dingTalkWebhookUrl.trim().isEmpty()) {
        log.warn("钉钉Webhook URL未配置，无法发送预警消息");
        return;
    }

    // 使用线程池异步发送消息
    dingTalkExecutor.execute(() -> {
        try {
            String message = buildCephPressureAlertMessage(clusterCode, pressure);
            sendDingTalkMessageWithSdk(message);
            log.info("Ceph压力预警消息发送成功: clusterCode={}, pressure={}%", clusterCode, pressure);
        } catch (Exception e) {
            log.error("发送Ceph压力预警消息失败: clusterCode={}, pressure={}%", clusterCode, pressure, e);
        }
    });
}
```

#### 1.4 添加SDK发送方法
```java
/**
 * 使用钉钉SDK发送消息
 */
private void sendDingTalkMessageWithSdk(String message) {
    try {
        // 创建钉钉客户端
        DingTalkClient client = new DefaultDingTalkClient(dingTalkWebhookUrl);
        
        // 创建请求对象
        OapiRobotSendRequest request = new OapiRobotSendRequest();
        request.setMsgtype("markdown");
        
        // 设置markdown消息内容
        OapiRobotSendRequest.Markdown markdown = new OapiRobotSendRequest.Markdown();
        markdown.setTitle("Ceph集群压力预警");
        markdown.setText(message);
        request.setMarkdown(markdown);
        
        // 设置@相关人员（可选）
        OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
        at.setIsAtAll(false);
        request.setAt(at);

        // 发送消息
        OapiRobotSendResponse response = client.execute(request);
        
        if (response.isSuccess()) {
            log.info("钉钉消息发送成功: {}", response.getBody());
        } else {
            log.error("钉钉消息发送失败，错误码: {}, 错误信息: {}", response.getErrorCode(), response.getErrmsg());
            // 如果SDK发送失败，尝试使用原有方式发送
            log.info("尝试使用原有方式发送钉钉消息");
            sendDingTalkMessage(message);
        }
    } catch (Exception e) {
        log.error("使用SDK发送钉钉消息异常，尝试使用原有方式发送", e);
        // 如果SDK发送异常，使用原有方式发送
        try {
            sendDingTalkMessage(message);
        } catch (Exception fallbackException) {
            log.error("原有方式发送钉钉消息也失败", fallbackException);
            throw fallbackException;
        }
    }
}
```

### 2. CephPressureDataServiceImpl 类修改

#### 2.1 简化预警检查方法
```java
/**
 * 检查并发送预警
 */
private void checkAndSendAlert(String clusterCode, Double pressure) {
    try {
        if (pressure > ALERT_THRESHOLD) {
            log.warn("集群{}压力超过预警阈值: {}% > {}%", clusterCode, pressure, ALERT_THRESHOLD);

            // 发送钉钉预警消息（DingTalkAlertService内部已使用线程池异步处理）
            dingTalkAlertService.sendCephPressureAlert(clusterCode, pressure);
        }
    } catch (Exception e) {
        log.error("检查预警失败", e);
    }
}
```

### 3. pom.xml 依赖配置

#### 3.1 添加钉钉SDK依赖
```xml
<!-- 钉钉SDK -->
<dependency>
    <groupId>com.aliyun</groupId>
    <artifactId>alibaba-dingtalk-service-sdk</artifactId>
</dependency>
```

## 优化效果

### 1. 线程池优化效果
- **避免频繁创建线程**：使用线程池替代直接 `new Thread()`，减少线程创建和销毁的开销
- **控制并发数量**：通过线程池参数控制最大并发数，避免系统资源过度消耗
- **优雅关闭**：应用关闭时能够优雅地关闭线程池，避免资源泄露
- **异常处理**：使用 `CallerRunsPolicy` 拒绝策略，确保在线程池满载时任务仍能执行

### 2. 钉钉SDK优化效果
- **标准化API调用**：使用官方SDK替代手工构建HTTP请求，提高代码可维护性
- **更好的错误处理**：SDK提供更详细的错误信息和状态码
- **向后兼容**：保留原有的RestTemplate发送方式作为备用，确保系统稳定性
- **代码简化**：减少手工构建消息体的复杂度

### 3. 系统稳定性提升
- **资源管理**：线程池统一管理线程资源，避免线程泄露
- **容错机制**：SDK发送失败时自动降级到原有方式
- **监控友好**：线程池提供更好的监控和调试能力

## 注意事项

1. **线程池配置**：根据实际业务量调整线程池参数（核心线程数、最大线程数、队列大小）
2. **依赖版本**：确保 `alibaba-dingtalk-service-sdk` 版本与项目其他依赖兼容
3. **配置验证**：部署前验证钉钉webhook URL配置正确性
4. **监控告警**：建议添加线程池监控，及时发现线程池异常情况

## 测试建议

1. **功能测试**：验证预警消息能正常发送到钉钉群
2. **性能测试**：测试高并发场景下线程池的表现
3. **异常测试**：测试网络异常、钉钉服务异常等场景下的降级机制
4. **资源测试**：验证应用关闭时线程池能正确释放资源
