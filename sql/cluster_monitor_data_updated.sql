CREATE TABLE `cluster_monitor_raw_data` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `cluster_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '集群编码',
    `metric_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '指标类型：ceph_pressure(压力值)',
    `metric_value` double NOT NULL COMMENT '指标值',
    `metric_unit` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '指标单位',
    `report_time` datetime NOT NULL COMMENT '上报时间',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_cluster_metric_time` (`report_time`, `metric_type`, `cluster_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='集群监控原始数据表';


CREATE TABLE `cluster_monitor_minute_data` (
   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
   `cluster_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '集群编码',
   `metric_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '指标类型：ceph_pressure(压力值)',
   `minute_time` datetime NOT NULL COMMENT '分钟时间点（精确到分钟）',
   `avg_value` double NOT NULL COMMENT '平均值',
   `max_value` double NOT NULL COMMENT '最大值',
   `min_value` double NOT NULL COMMENT '最小值',
   `data_count` int(11) NOT NULL DEFAULT '0' COMMENT '原始数据点数量',
   `metric_unit` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '指标单位',
   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   PRIMARY KEY (`id`),
   UNIQUE KEY `uk_cluster_metric_minute` (`minute_time`, `metric_type`, `cluster_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='集群监控分钟维度聚合表';


