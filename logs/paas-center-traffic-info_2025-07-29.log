[] paas-center-traffic-info 2025-07-29 14:35:14.388 [background-preinit] INFO  org.hibernate.validator.internal.util.Version -| HV000001: Hibernate Validator 6.2.5.Final
[] paas-center-traffic-info 2025-07-29 14:35:14.425 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| Starting PaasCenterTrafficInfoApplication using Java 1.8.0_252 on DESKTOP-D51FIJ4 with PID 44236 (D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes started by xskj in D:\dev\workspace\paas-center-traffic-info)
[] paas-center-traffic-info 2025-07-29 14:35:14.426 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| The following 1 profile is active: "docker"
[] paas-center-traffic-info 2025-07-29 14:35:14.489 [main] INFO  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader -| [Nacos Config] Load config[dataId=paas-center-traffic-info-docker.yaml, group=armcloud-paas-docker] success
[] paas-center-traffic-info 2025-07-29 14:35:15.578 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Multiple Spring Data modules found, entering strict repository configuration mode
[] paas-center-traffic-info 2025-07-29 14:35:15.581 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[] paas-center-traffic-info 2025-07-29 14:35:15.609 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
[] paas-center-traffic-info 2025-07-29 14:35:15.884 [main] INFO  org.springframework.cloud.context.scope.GenericScope -| BeanFactory id=24e5d0b6-9883-366d-a264-af4bafc3a277
[] paas-center-traffic-info 2025-07-29 14:35:16.243 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$EnhancerBySpringCGLIB$$620be03a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:35:16.253 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$EnhancerBySpringCGLIB$$ad1e99e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:35:16.483 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:35:16.498 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:35:16.500 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:35:16.502 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$537/890547325] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:35:16.503 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:35:16.517 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:35:16.525 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:35:17.099 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer -| Tomcat initialized with port(s): 18190 (http)
[] paas-center-traffic-info 2025-07-29 14:35:17.120 [main] INFO  org.apache.coyote.http11.Http11NioProtocol -| Initializing ProtocolHandler ["http-nio-18190"]
[] paas-center-traffic-info 2025-07-29 14:35:17.121 [main] INFO  org.apache.catalina.core.StandardService -| Starting service [Tomcat]
[] paas-center-traffic-info 2025-07-29 14:35:17.121 [main] INFO  org.apache.catalina.core.StandardEngine -| Starting Servlet engine: [Apache Tomcat/9.0.68]
[] paas-center-traffic-info 2025-07-29 14:35:17.430 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] -| Initializing Spring embedded WebApplicationContext
[] paas-center-traffic-info 2025-07-29 14:35:17.431 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext -| Root WebApplicationContext: initialization completed in 2939 ms
[] paas-center-traffic-info 2025-07-29 14:35:17.949 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 初始化ClickHouse数据源...
[] paas-center-traffic-info 2025-07-29 14:35:17.950 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 成功加载 ClickHouse 驱动类
[] paas-center-traffic-info 2025-07-29 14:35:17.950 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| ClickHouse DataSource bean created successfully
[] paas-center-traffic-info 2025-07-29 14:35:17.961 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 创建ClickHouse SQLSessionFactory, 数据源类型: com.zaxxer.hikari.HikariDataSource
[] paas-center-traffic-info 2025-07-29 14:35:18.368 [main] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoClickHouseService -| 初始化ClickHouse磁盘信息缓冲区, batchSize=100, flushInterval=10s, capacity=10000
[] paas-center-traffic-info 2025-07-29 14:35:18.405 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Property 'configuration' or 'configLocation' not specified, using default MyBatis Configuration
[] paas-center-traffic-info 2025-07-29 14:35:18.680 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\CephPressureDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:35:18.714 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\NetStoragePadUnitDetailMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:35:18.879 [main] DEBUG com.baomidou.mybatisplus.core.toolkit.Sequence -| Initialization Sequence datacenterId:0 workerId:5
[] paas-center-traffic-info 2025-07-29 14:35:19.645 [main] INFO  org.redisson.Version -| Redisson 3.17.2
[] paas-center-traffic-info 2025-07-29 14:35:21.146 [redisson-netty-2-8] INFO  org.redisson.connection.pool.MasterPubSubConnectionPool -| 1 connections initialized for **************/**************:6379
[] paas-center-traffic-info 2025-07-29 14:35:21.192 [redisson-netty-2-19] INFO  org.redisson.connection.pool.MasterConnectionPool -| 24 connections initialized for **************/**************:6379
[] paas-center-traffic-info 2025-07-29 14:35:21.699 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Property 'configuration' or 'configLocation' not specified, using default MyBatis Configuration
[] paas-center-traffic-info 2025-07-29 14:35:21.712 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\traffic\DeviceSystemConfigDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:35:21.720 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\traffic\PadSystemConfigDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:35:21.729 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\traffic\PadTrafficInfoMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:35:21.932 [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean -| For 'paas-center-core' URL not provided. Will try picking an instance via load-balancing.
[] paas-center-traffic-info 2025-07-29 14:35:22.219 [main] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Starting...
[] paas-center-traffic-info 2025-07-29 14:35:22.711 [main] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Start completed.
[] paas-center-traffic-info 2025-07-29 14:35:23.298 [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean -| For 'paas-center-core' URL not provided. Will try picking an instance via load-balancing.
[] paas-center-traffic-info 2025-07-29 14:35:25.979 [main] INFO  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration -| a producer (test_topic_producer_ali_sdk) init on namesrv **************:9876
[] paas-center-traffic-info 2025-07-29 14:35:28.832 [main] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingProducer -| 》》》》  Initialized loading RocketMQ producer Success 》》》》
[] paas-center-traffic-info 2025-07-29 14:35:31.078 [main] INFO  org.springframework.cloud.commons.util.InetUtils -| Cannot determine local hostname
[] paas-center-traffic-info 2025-07-29 14:35:33.302 [main] INFO  org.springframework.cloud.commons.util.InetUtils -| Cannot determine local hostname
[] paas-center-traffic-info 2025-07-29 14:35:33.434 [main] WARN  org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger -| Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[] paas-center-traffic-info 2025-07-29 14:35:33.444 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver -| Exposing 2 endpoint(s) beneath base path '/actuator'
[] paas-center-traffic-info 2025-07-29 14:35:33.538 [main] INFO  org.apache.coyote.http11.Http11NioProtocol -| Starting ProtocolHandler ["http-nio-18190"]
[] paas-center-traffic-info 2025-07-29 14:35:33.564 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer -| Tomcat started on port(s): 18190 (http) with context path ''
[] paas-center-traffic-info 2025-07-29 14:35:33.607 [main] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[] paas-center-traffic-info 2025-07-29 14:35:33.607 [main] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[] paas-center-traffic-info 2025-07-29 14:35:33.762 [main] INFO  com.alibaba.cloud.nacos.registry.NacosServiceRegistry -| nacos registry, armcloud-paas-docker paas-center-traffic-info *************:18190 register finished
[] paas-center-traffic-info 2025-07-29 14:35:34.934 [main] INFO  org.springframework.cloud.commons.util.InetUtils -| Cannot determine local hostname
[] paas-center-traffic-info 2025-07-29 14:35:34.953 [main] INFO  net.armcloud.paascenter.traffic.info.service.impl.DiskInfoServiceImpl -| 初始化ClickHouse磁盘信息缓冲区, batchSize=1000, flushInterval=2s, capacity=10000
[] paas-center-traffic-info 2025-07-29 14:35:35.030 [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean -| For 'paas-center-core' URL not provided. Will try picking an instance via load-balancing.
[] paas-center-traffic-info 2025-07-29 14:35:35.041 [main] INFO  net.armcloud.paascenter.traffic.info.service.impl.PadTrafficInfoServiceImpl -| 初始化磁盘信息缓冲区, batchSize=10, flushInterval=5s, capacity=1000
[] paas-center-traffic-info 2025-07-29 14:35:35.069 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| Started PaasCenterTrafficInfoApplication in 23.933 seconds (JVM running for 26.974)
[] paas-center-traffic-info 2025-07-29 14:35:35.086 [main] DEBUG com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner -|   ...  DDL start create  ...  
[] paas-center-traffic-info 2025-07-29 14:35:35.087 [main] DEBUG com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner -|   ...  DDL end create  ...  
[] paas-center-traffic-info 2025-07-29 14:35:35.087 [main] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initializing rocketmq consumer...
[] paas-center-traffic-info 2025-07-29 14:35:35.089 [rocketMqConsumer-1-t-1] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic cbs_traffic_disk_data 
[] paas-center-traffic-info 2025-07-29 14:35:35.089 [rocketMqConsumer-1-t-2] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic device_system_config_data 
[] paas-center-traffic-info 2025-07-29 14:35:35.089 [rocketMqConsumer-1-t-3] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic pad_system_config_data 
[] paas-center-traffic-info 2025-07-29 14:35:35.089 [rocketMqConsumer-1-t-5] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic pad_traffic_info_data 
[] paas-center-traffic-info 2025-07-29 14:35:35.089 [rocketMqConsumer-1-t-4] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic pad_traffic_data 
[] paas-center-traffic-info 2025-07-29 14:35:35.190 [main] INFO  com.alibaba.cloud.nacos.refresh.NacosContextRefresher -| [Nacos Config] Listening config: dataId=paas-center-traffic-info-docker.yaml, group=armcloud-paas-docker
[] paas-center-traffic-info 2025-07-29 14:35:35.567 [RMI TCP Connection(7)-************] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
[] paas-center-traffic-info 2025-07-29 14:35:35.568 [RMI TCP Connection(7)-************] INFO  org.springframework.web.servlet.DispatcherServlet -| Initializing Servlet 'dispatcherServlet'
[] paas-center-traffic-info 2025-07-29 14:35:35.570 [RMI TCP Connection(7)-************] INFO  org.springframework.web.servlet.DispatcherServlet -| Completed initialization in 2 ms
[] paas-center-traffic-info 2025-07-29 14:35:35.897 [RMI TCP Connection(9)-************] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-2 - Starting...
[] paas-center-traffic-info 2025-07-29 14:35:39.453 [RMI TCP Connection(9)-************] ERROR com.zaxxer.hikari.pool.HikariPool -| HikariPool-2 - Exception during pool initialization.
java.sql.SQLException: Connection refused: connect, server ClickHouseNode [uri=http://localhost:8123/armcloud, options={distributed_connections_pool_size=20,compress=1,max_parallel_replicas=2,keep_alive_timeout=30000,data_transfer_timeout=120000,max_partitions_per_insert_block=100,load_balancing=roundrobin,socket_timeout=60000,connection_timeout=30000}]@**********
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:85)
	at com.clickhouse.jdbc.SqlExceptionUtils.create(SqlExceptionUtils.java:31)
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:90)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:131)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:335)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:288)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:157)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:41)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:330)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.getProduct(DataSourceHealthIndicator.java:122)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doDataSourceHealthCheck(DataSourceHealthIndicator.java:105)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doHealthCheck(DataSourceHealthIndicator.java:100)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:122)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468)
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76)
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309)
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401)
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at sun.reflect.GeneratedMethodAccessor105.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357)
	at sun.rmi.transport.Transport$1.run(Transport.java:200)
	at sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1340)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1315)
	at com.clickhouse.client.http.HttpUrlConnectionImpl.post(HttpUrlConnectionImpl.java:225)
	at com.clickhouse.client.http.ClickHouseHttpClient.send(ClickHouseHttpClient.java:124)
	at com.clickhouse.client.AbstractClient.execute(AbstractClient.java:280)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.sendOnce(ClickHouseClientBuilder.java:282)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.send(ClickHouseClientBuilder.java:294)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.execute(ClickHouseClientBuilder.java:349)
	at com.clickhouse.client.ClickHouseClient.executeAndWait(ClickHouseClient.java:1056)
	at com.clickhouse.client.ClickHouseRequest.executeAndWait(ClickHouseRequest.java:2154)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:128)
	... 64 common frames omitted
[] paas-center-traffic-info 2025-07-29 14:35:39.458 [RMI TCP Connection(9)-************] WARN  org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator -| DataSource health check failed
org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException: Connection refused: connect, server ClickHouseNode [uri=http://localhost:8123/armcloud, options={distributed_connections_pool_size=20,compress=1,max_parallel_replicas=2,keep_alive_timeout=30000,data_transfer_timeout=120000,max_partitions_per_insert_block=100,load_balancing=roundrobin,socket_timeout=60000,connection_timeout=30000}]@**********
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:83)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:330)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.getProduct(DataSourceHealthIndicator.java:122)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doDataSourceHealthCheck(DataSourceHealthIndicator.java:105)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doHealthCheck(DataSourceHealthIndicator.java:100)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:122)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468)
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76)
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309)
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401)
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at sun.reflect.GeneratedMethodAccessor105.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357)
	at sun.rmi.transport.Transport$1.run(Transport.java:200)
	at sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLException: Connection refused: connect, server ClickHouseNode [uri=http://localhost:8123/armcloud, options={distributed_connections_pool_size=20,compress=1,max_parallel_replicas=2,keep_alive_timeout=30000,data_transfer_timeout=120000,max_partitions_per_insert_block=100,load_balancing=roundrobin,socket_timeout=60000,connection_timeout=30000}]@**********
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:85)
	at com.clickhouse.jdbc.SqlExceptionUtils.create(SqlExceptionUtils.java:31)
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:90)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:131)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:335)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:288)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:157)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:41)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	... 50 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1340)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1315)
	at com.clickhouse.client.http.HttpUrlConnectionImpl.post(HttpUrlConnectionImpl.java:225)
	at com.clickhouse.client.http.ClickHouseHttpClient.send(ClickHouseHttpClient.java:124)
	at com.clickhouse.client.AbstractClient.execute(AbstractClient.java:280)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.sendOnce(ClickHouseClientBuilder.java:282)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.send(ClickHouseClientBuilder.java:294)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.execute(ClickHouseClientBuilder.java:349)
	at com.clickhouse.client.ClickHouseClient.executeAndWait(ClickHouseClient.java:1056)
	at com.clickhouse.client.ClickHouseRequest.executeAndWait(ClickHouseRequest.java:2154)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:128)
	... 64 common frames omitted
[] paas-center-traffic-info 2025-07-29 14:35:39.458 [RMI TCP Connection(9)-************] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Starting...
[] paas-center-traffic-info 2025-07-29 14:35:39.465 [RMI TCP Connection(9)-************] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Start completed.
[] paas-center-traffic-info 2025-07-29 14:37:28.865 [http-nio-18190-exec-2] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| ceph压力数据上报:{"deviceType":"CBS","clusterCode":"001","metrics":{"cephPressure":"32.12"}}
[] paas-center-traffic-info 2025-07-29 14:37:29.441 [http-nio-18190-exec-2] ERROR net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 保存Ceph压力数据失败
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'create_by' in 'field list'
### The error may exist in net/armcloud/paascenter/traffic/info/mapper/paas/ClusterMonitorDataMapper.java (best guess)
### The error may involve net.armcloud.paascenter.traffic.info.mapper.paas.ClusterMonitorDataMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO cluster_monitor_raw_data  ( cluster_code, metric_type, metric_value, metric_unit, report_time, create_time, create_by )  VALUES (  ?, ?, ?, ?, ?, ?, ?  )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'create_by' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'create_by' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy115.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy123.insert(Unknown Source)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl.saveCephPressureData(CephPressureDataServiceImpl.java:57)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl$$FastClassBySpringCGLIB$$c3d291d4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl$$EnhancerBySpringCGLIB$$32c45b74.saveCephPressureData(<generated>)
	at net.armcloud.paascenter.traffic.info.controller.TrafficDataController.cephReport(TrafficDataController.java:99)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.armcloud.paascenter.traffic.info.filter.RequestBodyCacheFilter.doFilter(RequestBodyCacheFilter.java:26)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'create_by' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 77 common frames omitted
[] paas-center-traffic-info 2025-07-29 14:37:29.447 [http-nio-18190-exec-2] ERROR net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| Ceph压力数据上报处理异常
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'create_by' in 'field list'
### The error may exist in net/armcloud/paascenter/traffic/info/mapper/paas/ClusterMonitorDataMapper.java (best guess)
### The error may involve net.armcloud.paascenter.traffic.info.mapper.paas.ClusterMonitorDataMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO cluster_monitor_raw_data  ( cluster_code, metric_type, metric_value, metric_unit, report_time, create_time, create_by )  VALUES (  ?, ?, ?, ?, ?, ?, ?  )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'create_by' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'create_by' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy115.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy123.insert(Unknown Source)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl.saveCephPressureData(CephPressureDataServiceImpl.java:57)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl$$FastClassBySpringCGLIB$$c3d291d4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl$$EnhancerBySpringCGLIB$$32c45b74.saveCephPressureData(<generated>)
	at net.armcloud.paascenter.traffic.info.controller.TrafficDataController.cephReport(TrafficDataController.java:99)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.armcloud.paascenter.traffic.info.filter.RequestBodyCacheFilter.doFilter(RequestBodyCacheFilter.java:26)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'create_by' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 77 common frames omitted
[] paas-center-traffic-info 2025-07-29 14:38:05.229 [Thread-6] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder -| [HttpClientBeanHolder] Start destroying common HttpClient
[] paas-center-traffic-info 2025-07-29 14:38:05.229 [Thread-11] WARN  com.alibaba.nacos.common.notify.NotifyCenter -| [NotifyCenter] Start destroying Publisher
[] paas-center-traffic-info 2025-07-29 14:38:05.230 [Thread-11] WARN  com.alibaba.nacos.common.notify.NotifyCenter -| [NotifyCenter] Destruction of the end
[] paas-center-traffic-info 2025-07-29 14:38:05.230 [Thread-6] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder -| [HttpClientBeanHolder] Destruction of the end
[] paas-center-traffic-info 2025-07-29 14:38:05.390 [SpringApplicationShutdownHook] INFO  com.alibaba.cloud.nacos.registry.NacosServiceRegistry -| De-registering from Nacos Server now...
[] paas-center-traffic-info 2025-07-29 14:38:05.392 [SpringApplicationShutdownHook] INFO  com.alibaba.cloud.nacos.registry.NacosServiceRegistry -| De-registration finished.
[] paas-center-traffic-info 2025-07-29 14:38:05.437 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoSendMsgService -| 关闭缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 14:38:08.596 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Shutdown initiated...
[] paas-center-traffic-info 2025-07-29 14:38:08.610 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Shutdown completed.
[] paas-center-traffic-info 2025-07-29 14:38:08.611 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoClickHouseService -| 关闭ClickHouse磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 14:38:08.611 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Shutdown initiated...
[] paas-center-traffic-info 2025-07-29 14:38:08.618 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Shutdown completed.
[] paas-center-traffic-info 2025-07-29 14:38:08.619 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.impl.DiskInfoServiceImpl -| 关闭ClickHouse磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 14:38:08.619 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.impl.PadTrafficInfoServiceImpl -| 关闭磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 14:38:13.917 [background-preinit] INFO  org.hibernate.validator.internal.util.Version -| HV000001: Hibernate Validator 6.2.5.Final
[] paas-center-traffic-info 2025-07-29 14:38:13.937 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| Starting PaasCenterTrafficInfoApplication using Java 1.8.0_252 on DESKTOP-D51FIJ4 with PID 3248 (D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes started by xskj in D:\dev\workspace\paas-center-traffic-info)
[] paas-center-traffic-info 2025-07-29 14:38:13.937 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| The following 1 profile is active: "docker"
[] paas-center-traffic-info 2025-07-29 14:38:13.994 [main] INFO  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader -| [Nacos Config] Load config[dataId=paas-center-traffic-info-docker.yaml, group=armcloud-paas-docker] success
[] paas-center-traffic-info 2025-07-29 14:38:14.975 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Multiple Spring Data modules found, entering strict repository configuration mode
[] paas-center-traffic-info 2025-07-29 14:38:14.978 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[] paas-center-traffic-info 2025-07-29 14:38:15.005 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
[] paas-center-traffic-info 2025-07-29 14:38:15.266 [main] INFO  org.springframework.cloud.context.scope.GenericScope -| BeanFactory id=24e5d0b6-9883-366d-a264-af4bafc3a277
[] paas-center-traffic-info 2025-07-29 14:38:15.528 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$EnhancerBySpringCGLIB$$52dc7df4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:38:15.533 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$EnhancerBySpringCGLIB$$fba28758] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:38:15.659 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:38:15.667 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:38:15.668 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:38:15.669 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$537/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:38:15.670 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:38:15.678 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:38:15.683 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:38:16.000 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer -| Tomcat initialized with port(s): 18190 (http)
[] paas-center-traffic-info 2025-07-29 14:38:16.013 [main] INFO  org.apache.coyote.http11.Http11NioProtocol -| Initializing ProtocolHandler ["http-nio-18190"]
[] paas-center-traffic-info 2025-07-29 14:38:16.014 [main] INFO  org.apache.catalina.core.StandardService -| Starting service [Tomcat]
[] paas-center-traffic-info 2025-07-29 14:38:16.014 [main] INFO  org.apache.catalina.core.StandardEngine -| Starting Servlet engine: [Apache Tomcat/9.0.68]
[] paas-center-traffic-info 2025-07-29 14:38:16.174 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] -| Initializing Spring embedded WebApplicationContext
[] paas-center-traffic-info 2025-07-29 14:38:16.175 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext -| Root WebApplicationContext: initialization completed in 2178 ms
[] paas-center-traffic-info 2025-07-29 14:38:16.467 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 初始化ClickHouse数据源...
[] paas-center-traffic-info 2025-07-29 14:38:16.468 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 成功加载 ClickHouse 驱动类
[] paas-center-traffic-info 2025-07-29 14:38:16.468 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| ClickHouse DataSource bean created successfully
[] paas-center-traffic-info 2025-07-29 14:38:16.472 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 创建ClickHouse SQLSessionFactory, 数据源类型: com.zaxxer.hikari.HikariDataSource
[] paas-center-traffic-info 2025-07-29 14:38:16.701 [main] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoClickHouseService -| 初始化ClickHouse磁盘信息缓冲区, batchSize=100, flushInterval=10s, capacity=10000
[] paas-center-traffic-info 2025-07-29 14:38:16.730 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Property 'configuration' or 'configLocation' not specified, using default MyBatis Configuration
[] paas-center-traffic-info 2025-07-29 14:38:16.870 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\CephPressureDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:38:16.888 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\NetStoragePadUnitDetailMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:38:17.011 [main] DEBUG com.baomidou.mybatisplus.core.toolkit.Sequence -| Initialization Sequence datacenterId:0 workerId:19
[] paas-center-traffic-info 2025-07-29 14:38:17.514 [main] INFO  org.redisson.Version -| Redisson 3.17.2
[] paas-center-traffic-info 2025-07-29 14:38:18.286 [redisson-netty-2-17] INFO  org.redisson.connection.pool.MasterPubSubConnectionPool -| 1 connections initialized for **************/**************:6379
[] paas-center-traffic-info 2025-07-29 14:38:18.327 [redisson-netty-2-19] INFO  org.redisson.connection.pool.MasterConnectionPool -| 24 connections initialized for **************/**************:6379
[] paas-center-traffic-info 2025-07-29 14:38:18.601 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Property 'configuration' or 'configLocation' not specified, using default MyBatis Configuration
[] paas-center-traffic-info 2025-07-29 14:38:18.609 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\traffic\DeviceSystemConfigDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:38:18.612 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\traffic\PadSystemConfigDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:38:18.614 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\traffic\PadTrafficInfoMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:38:18.728 [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean -| For 'paas-center-core' URL not provided. Will try picking an instance via load-balancing.
[] paas-center-traffic-info 2025-07-29 14:38:18.902 [main] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Starting...
[] paas-center-traffic-info 2025-07-29 14:38:19.232 [main] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Start completed.
[] paas-center-traffic-info 2025-07-29 14:38:19.440 [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean -| For 'paas-center-core' URL not provided. Will try picking an instance via load-balancing.
[] paas-center-traffic-info 2025-07-29 14:38:20.327 [main] INFO  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration -| a producer (test_topic_producer_ali_sdk) init on namesrv **************:9876
[] paas-center-traffic-info 2025-07-29 14:38:21.588 [main] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingProducer -| 》》》》  Initialized loading RocketMQ producer Success 》》》》
[] paas-center-traffic-info 2025-07-29 14:38:23.222 [main] INFO  org.springframework.cloud.commons.util.InetUtils -| Cannot determine local hostname
[] paas-center-traffic-info 2025-07-29 14:38:25.390 [main] INFO  org.springframework.cloud.commons.util.InetUtils -| Cannot determine local hostname
[] paas-center-traffic-info 2025-07-29 14:38:25.516 [main] WARN  org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger -| Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[] paas-center-traffic-info 2025-07-29 14:38:25.526 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver -| Exposing 2 endpoint(s) beneath base path '/actuator'
[] paas-center-traffic-info 2025-07-29 14:38:25.619 [main] INFO  org.apache.coyote.http11.Http11NioProtocol -| Starting ProtocolHandler ["http-nio-18190"]
[] paas-center-traffic-info 2025-07-29 14:38:25.642 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer -| Tomcat started on port(s): 18190 (http) with context path ''
[] paas-center-traffic-info 2025-07-29 14:38:26.805 [main] INFO  org.springframework.cloud.commons.util.InetUtils -| Cannot determine local hostname
[] paas-center-traffic-info 2025-07-29 14:38:26.822 [main] INFO  net.armcloud.paascenter.traffic.info.service.impl.DiskInfoServiceImpl -| 初始化ClickHouse磁盘信息缓冲区, batchSize=1000, flushInterval=2s, capacity=10000
[] paas-center-traffic-info 2025-07-29 14:38:26.894 [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean -| For 'paas-center-core' URL not provided. Will try picking an instance via load-balancing.
[] paas-center-traffic-info 2025-07-29 14:38:26.905 [main] INFO  net.armcloud.paascenter.traffic.info.service.impl.PadTrafficInfoServiceImpl -| 初始化磁盘信息缓冲区, batchSize=10, flushInterval=5s, capacity=1000
[] paas-center-traffic-info 2025-07-29 14:38:26.932 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| Started PaasCenterTrafficInfoApplication in 16.341 seconds (JVM running for 17.273)
[] paas-center-traffic-info 2025-07-29 14:38:26.949 [main] DEBUG com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner -|   ...  DDL start create  ...  
[] paas-center-traffic-info 2025-07-29 14:38:26.951 [main] DEBUG com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner -|   ...  DDL end create  ...  
[] paas-center-traffic-info 2025-07-29 14:38:26.951 [main] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initializing rocketmq consumer...
[] paas-center-traffic-info 2025-07-29 14:38:26.952 [rocketMqConsumer-1-t-2] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic device_system_config_data 
[] paas-center-traffic-info 2025-07-29 14:38:26.952 [rocketMqConsumer-1-t-1] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic cbs_traffic_disk_data 
[] paas-center-traffic-info 2025-07-29 14:38:26.952 [rocketMqConsumer-1-t-4] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic pad_traffic_data 
[] paas-center-traffic-info 2025-07-29 14:38:26.952 [rocketMqConsumer-1-t-5] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic pad_traffic_info_data 
[] paas-center-traffic-info 2025-07-29 14:38:26.952 [rocketMqConsumer-1-t-3] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic pad_system_config_data 
[] paas-center-traffic-info 2025-07-29 14:38:27.051 [main] INFO  com.alibaba.cloud.nacos.refresh.NacosContextRefresher -| [Nacos Config] Listening config: dataId=paas-center-traffic-info-docker.yaml, group=armcloud-paas-docker
[] paas-center-traffic-info 2025-07-29 14:38:27.446 [RMI TCP Connection(3)-************] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
[] paas-center-traffic-info 2025-07-29 14:38:27.446 [RMI TCP Connection(3)-************] INFO  org.springframework.web.servlet.DispatcherServlet -| Initializing Servlet 'dispatcherServlet'
[] paas-center-traffic-info 2025-07-29 14:38:27.447 [RMI TCP Connection(3)-************] INFO  org.springframework.web.servlet.DispatcherServlet -| Completed initialization in 1 ms
[] paas-center-traffic-info 2025-07-29 14:38:27.870 [RMI TCP Connection(6)-************] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[] paas-center-traffic-info 2025-07-29 14:38:27.870 [RMI TCP Connection(6)-************] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[] paas-center-traffic-info 2025-07-29 14:38:28.029 [RMI TCP Connection(6)-************] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-2 - Starting...
[] paas-center-traffic-info 2025-07-29 14:38:31.446 [RMI TCP Connection(6)-************] ERROR com.zaxxer.hikari.pool.HikariPool -| HikariPool-2 - Exception during pool initialization.
java.sql.SQLException: Connection refused: connect, server ClickHouseNode [uri=http://localhost:8123/armcloud, options={distributed_connections_pool_size=20,compress=1,max_parallel_replicas=2,keep_alive_timeout=30000,data_transfer_timeout=120000,max_partitions_per_insert_block=100,load_balancing=roundrobin,socket_timeout=60000,connection_timeout=30000}]@-**********
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:85)
	at com.clickhouse.jdbc.SqlExceptionUtils.create(SqlExceptionUtils.java:31)
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:90)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:131)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:335)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:288)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:157)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:41)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:330)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.getProduct(DataSourceHealthIndicator.java:122)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doDataSourceHealthCheck(DataSourceHealthIndicator.java:105)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doHealthCheck(DataSourceHealthIndicator.java:100)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:122)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468)
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76)
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309)
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401)
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at sun.reflect.GeneratedMethodAccessor100.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357)
	at sun.rmi.transport.Transport$1.run(Transport.java:200)
	at sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1340)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1315)
	at com.clickhouse.client.http.HttpUrlConnectionImpl.post(HttpUrlConnectionImpl.java:225)
	at com.clickhouse.client.http.ClickHouseHttpClient.send(ClickHouseHttpClient.java:124)
	at com.clickhouse.client.AbstractClient.execute(AbstractClient.java:280)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.sendOnce(ClickHouseClientBuilder.java:282)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.send(ClickHouseClientBuilder.java:294)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.execute(ClickHouseClientBuilder.java:349)
	at com.clickhouse.client.ClickHouseClient.executeAndWait(ClickHouseClient.java:1056)
	at com.clickhouse.client.ClickHouseRequest.executeAndWait(ClickHouseRequest.java:2154)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:128)
	... 64 common frames omitted
[] paas-center-traffic-info 2025-07-29 14:38:31.462 [RMI TCP Connection(6)-************] WARN  org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator -| DataSource health check failed
org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException: Connection refused: connect, server ClickHouseNode [uri=http://localhost:8123/armcloud, options={distributed_connections_pool_size=20,compress=1,max_parallel_replicas=2,keep_alive_timeout=30000,data_transfer_timeout=120000,max_partitions_per_insert_block=100,load_balancing=roundrobin,socket_timeout=60000,connection_timeout=30000}]@-**********
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:83)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:330)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.getProduct(DataSourceHealthIndicator.java:122)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doDataSourceHealthCheck(DataSourceHealthIndicator.java:105)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doHealthCheck(DataSourceHealthIndicator.java:100)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:122)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468)
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76)
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309)
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401)
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at sun.reflect.GeneratedMethodAccessor100.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357)
	at sun.rmi.transport.Transport$1.run(Transport.java:200)
	at sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLException: Connection refused: connect, server ClickHouseNode [uri=http://localhost:8123/armcloud, options={distributed_connections_pool_size=20,compress=1,max_parallel_replicas=2,keep_alive_timeout=30000,data_transfer_timeout=120000,max_partitions_per_insert_block=100,load_balancing=roundrobin,socket_timeout=60000,connection_timeout=30000}]@-**********
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:85)
	at com.clickhouse.jdbc.SqlExceptionUtils.create(SqlExceptionUtils.java:31)
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:90)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:131)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:335)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:288)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:157)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:41)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	... 50 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1340)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1315)
	at com.clickhouse.client.http.HttpUrlConnectionImpl.post(HttpUrlConnectionImpl.java:225)
	at com.clickhouse.client.http.ClickHouseHttpClient.send(ClickHouseHttpClient.java:124)
	at com.clickhouse.client.AbstractClient.execute(AbstractClient.java:280)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.sendOnce(ClickHouseClientBuilder.java:282)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.send(ClickHouseClientBuilder.java:294)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.execute(ClickHouseClientBuilder.java:349)
	at com.clickhouse.client.ClickHouseClient.executeAndWait(ClickHouseClient.java:1056)
	at com.clickhouse.client.ClickHouseRequest.executeAndWait(ClickHouseRequest.java:2154)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:128)
	... 64 common frames omitted
[] paas-center-traffic-info 2025-07-29 14:38:31.465 [RMI TCP Connection(6)-************] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Starting...
[] paas-center-traffic-info 2025-07-29 14:38:31.475 [RMI TCP Connection(6)-************] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Start completed.
[] paas-center-traffic-info 2025-07-29 14:38:49.666 [http-nio-18190-exec-9] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| ceph压力数据上报:{"deviceType":"CBS","clusterCode":"001","metrics":{"cephPressure":"32.12"}}
[] paas-center-traffic-info 2025-07-29 14:38:50.127 [http-nio-18190-exec-9] ERROR net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 保存Ceph压力数据失败
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'create_by' in 'field list'
### The error may exist in net/armcloud/paascenter/traffic/info/mapper/paas/ClusterMonitorDataMapper.java (best guess)
### The error may involve net.armcloud.paascenter.traffic.info.mapper.paas.ClusterMonitorDataMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO cluster_monitor_raw_data  ( cluster_code, metric_type, metric_value, metric_unit, report_time, create_time, create_by )  VALUES (  ?, ?, ?, ?, ?, ?, ?  )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'create_by' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'create_by' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy115.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy123.insert(Unknown Source)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl.saveCephPressureData(CephPressureDataServiceImpl.java:57)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl$$FastClassBySpringCGLIB$$c3d291d4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl$$EnhancerBySpringCGLIB$$c6b2c765.saveCephPressureData(<generated>)
	at net.armcloud.paascenter.traffic.info.controller.TrafficDataController.cephReport(TrafficDataController.java:99)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.armcloud.paascenter.traffic.info.filter.RequestBodyCacheFilter.doFilter(RequestBodyCacheFilter.java:26)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'create_by' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 77 common frames omitted
[] paas-center-traffic-info 2025-07-29 14:38:50.130 [http-nio-18190-exec-9] ERROR net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| Ceph压力数据上报处理异常
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'create_by' in 'field list'
### The error may exist in net/armcloud/paascenter/traffic/info/mapper/paas/ClusterMonitorDataMapper.java (best guess)
### The error may involve net.armcloud.paascenter.traffic.info.mapper.paas.ClusterMonitorDataMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO cluster_monitor_raw_data  ( cluster_code, metric_type, metric_value, metric_unit, report_time, create_time, create_by )  VALUES (  ?, ?, ?, ?, ?, ?, ?  )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'create_by' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'create_by' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy115.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy123.insert(Unknown Source)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl.saveCephPressureData(CephPressureDataServiceImpl.java:57)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl$$FastClassBySpringCGLIB$$c3d291d4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl$$EnhancerBySpringCGLIB$$c6b2c765.saveCephPressureData(<generated>)
	at net.armcloud.paascenter.traffic.info.controller.TrafficDataController.cephReport(TrafficDataController.java:99)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.armcloud.paascenter.traffic.info.filter.RequestBodyCacheFilter.doFilter(RequestBodyCacheFilter.java:26)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'create_by' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 77 common frames omitted
[] paas-center-traffic-info 2025-07-29 14:39:32.233 [Thread-4] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder -| [HttpClientBeanHolder] Start destroying common HttpClient
[] paas-center-traffic-info 2025-07-29 14:39:32.233 [Thread-10] WARN  com.alibaba.nacos.common.notify.NotifyCenter -| [NotifyCenter] Start destroying Publisher
[] paas-center-traffic-info 2025-07-29 14:39:32.234 [Thread-10] WARN  com.alibaba.nacos.common.notify.NotifyCenter -| [NotifyCenter] Destruction of the end
[] paas-center-traffic-info 2025-07-29 14:39:32.234 [Thread-4] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder -| [HttpClientBeanHolder] Destruction of the end
[] paas-center-traffic-info 2025-07-29 14:39:32.436 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoSendMsgService -| 关闭缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 14:39:35.541 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Shutdown initiated...
[] paas-center-traffic-info 2025-07-29 14:39:35.571 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Shutdown completed.
[] paas-center-traffic-info 2025-07-29 14:39:35.572 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoClickHouseService -| 关闭ClickHouse磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 14:39:35.572 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Shutdown initiated...
[] paas-center-traffic-info 2025-07-29 14:39:35.579 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Shutdown completed.
[] paas-center-traffic-info 2025-07-29 14:39:35.580 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.impl.DiskInfoServiceImpl -| 关闭ClickHouse磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 14:39:35.580 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.impl.PadTrafficInfoServiceImpl -| 关闭磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 14:40:19.929 [background-preinit] INFO  org.hibernate.validator.internal.util.Version -| HV000001: Hibernate Validator 6.2.5.Final
[] paas-center-traffic-info 2025-07-29 14:40:19.964 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| Starting PaasCenterTrafficInfoApplication using Java 1.8.0_252 on DESKTOP-D51FIJ4 with PID 29880 (D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes started by xskj in D:\dev\workspace\paas-center-traffic-info)
[] paas-center-traffic-info 2025-07-29 14:40:19.964 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| The following 1 profile is active: "docker"
[] paas-center-traffic-info 2025-07-29 14:40:20.029 [main] INFO  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader -| [Nacos Config] Load config[dataId=paas-center-traffic-info-docker.yaml, group=armcloud-paas-docker] success
[] paas-center-traffic-info 2025-07-29 14:40:20.955 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Multiple Spring Data modules found, entering strict repository configuration mode
[] paas-center-traffic-info 2025-07-29 14:40:20.958 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[] paas-center-traffic-info 2025-07-29 14:40:20.984 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
[] paas-center-traffic-info 2025-07-29 14:40:21.278 [main] INFO  org.springframework.cloud.context.scope.GenericScope -| BeanFactory id=24e5d0b6-9883-366d-a264-af4bafc3a277
[] paas-center-traffic-info 2025-07-29 14:40:21.575 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$EnhancerBySpringCGLIB$$a736c0a5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:40:21.595 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$EnhancerBySpringCGLIB$$4ffcca09] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:40:21.713 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:40:21.721 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:40:21.722 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:40:21.722 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$537/349961649] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:40:21.723 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:40:21.730 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:40:21.734 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:40:22.049 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer -| Tomcat initialized with port(s): 18190 (http)
[] paas-center-traffic-info 2025-07-29 14:40:22.061 [main] INFO  org.apache.coyote.http11.Http11NioProtocol -| Initializing ProtocolHandler ["http-nio-18190"]
[] paas-center-traffic-info 2025-07-29 14:40:22.062 [main] INFO  org.apache.catalina.core.StandardService -| Starting service [Tomcat]
[] paas-center-traffic-info 2025-07-29 14:40:22.062 [main] INFO  org.apache.catalina.core.StandardEngine -| Starting Servlet engine: [Apache Tomcat/9.0.68]
[] paas-center-traffic-info 2025-07-29 14:40:22.200 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] -| Initializing Spring embedded WebApplicationContext
[] paas-center-traffic-info 2025-07-29 14:40:22.200 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext -| Root WebApplicationContext: initialization completed in 2168 ms
[] paas-center-traffic-info 2025-07-29 14:40:22.490 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 初始化ClickHouse数据源...
[] paas-center-traffic-info 2025-07-29 14:40:22.490 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 成功加载 ClickHouse 驱动类
[] paas-center-traffic-info 2025-07-29 14:40:22.490 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| ClickHouse DataSource bean created successfully
[] paas-center-traffic-info 2025-07-29 14:40:22.494 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 创建ClickHouse SQLSessionFactory, 数据源类型: com.zaxxer.hikari.HikariDataSource
[] paas-center-traffic-info 2025-07-29 14:40:22.735 [main] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoClickHouseService -| 初始化ClickHouse磁盘信息缓冲区, batchSize=100, flushInterval=10s, capacity=10000
[] paas-center-traffic-info 2025-07-29 14:40:22.765 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Property 'configuration' or 'configLocation' not specified, using default MyBatis Configuration
[] paas-center-traffic-info 2025-07-29 14:40:22.918 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\CephPressureDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:40:22.935 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\NetStoragePadUnitDetailMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:40:23.052 [main] DEBUG com.baomidou.mybatisplus.core.toolkit.Sequence -| Initialization Sequence datacenterId:0 workerId:25
[] paas-center-traffic-info 2025-07-29 14:40:23.483 [main] INFO  org.redisson.Version -| Redisson 3.17.2
[] paas-center-traffic-info 2025-07-29 14:40:24.203 [redisson-netty-2-8] INFO  org.redisson.connection.pool.MasterPubSubConnectionPool -| 1 connections initialized for **************/**************:6379
[] paas-center-traffic-info 2025-07-29 14:40:24.228 [redisson-netty-2-19] INFO  org.redisson.connection.pool.MasterConnectionPool -| 24 connections initialized for **************/**************:6379
[] paas-center-traffic-info 2025-07-29 14:40:24.503 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Property 'configuration' or 'configLocation' not specified, using default MyBatis Configuration
[] paas-center-traffic-info 2025-07-29 14:40:24.510 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\traffic\DeviceSystemConfigDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:40:24.512 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\traffic\PadSystemConfigDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:40:24.515 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\traffic\PadTrafficInfoMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:40:24.636 [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean -| For 'paas-center-core' URL not provided. Will try picking an instance via load-balancing.
[] paas-center-traffic-info 2025-07-29 14:40:24.804 [main] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Starting...
[] paas-center-traffic-info 2025-07-29 14:40:25.090 [main] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Start completed.
[] paas-center-traffic-info 2025-07-29 14:40:25.288 [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean -| For 'paas-center-core' URL not provided. Will try picking an instance via load-balancing.
[] paas-center-traffic-info 2025-07-29 14:40:26.174 [main] INFO  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration -| a producer (test_topic_producer_ali_sdk) init on namesrv **************:9876
[] paas-center-traffic-info 2025-07-29 14:40:27.451 [main] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingProducer -| 》》》》  Initialized loading RocketMQ producer Success 》》》》
[] paas-center-traffic-info 2025-07-29 14:40:29.101 [main] INFO  org.springframework.cloud.commons.util.InetUtils -| Cannot determine local hostname
[] paas-center-traffic-info 2025-07-29 14:40:31.282 [main] INFO  org.springframework.cloud.commons.util.InetUtils -| Cannot determine local hostname
[] paas-center-traffic-info 2025-07-29 14:40:31.407 [main] WARN  org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger -| Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[] paas-center-traffic-info 2025-07-29 14:40:31.417 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver -| Exposing 2 endpoint(s) beneath base path '/actuator'
[] paas-center-traffic-info 2025-07-29 14:40:31.510 [main] INFO  org.apache.coyote.http11.Http11NioProtocol -| Starting ProtocolHandler ["http-nio-18190"]
[] paas-center-traffic-info 2025-07-29 14:40:31.532 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer -| Tomcat started on port(s): 18190 (http) with context path ''
[] paas-center-traffic-info 2025-07-29 14:40:32.696 [main] INFO  org.springframework.cloud.commons.util.InetUtils -| Cannot determine local hostname
[] paas-center-traffic-info 2025-07-29 14:40:32.715 [main] INFO  net.armcloud.paascenter.traffic.info.service.impl.DiskInfoServiceImpl -| 初始化ClickHouse磁盘信息缓冲区, batchSize=1000, flushInterval=2s, capacity=10000
[] paas-center-traffic-info 2025-07-29 14:40:32.786 [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean -| For 'paas-center-core' URL not provided. Will try picking an instance via load-balancing.
[] paas-center-traffic-info 2025-07-29 14:40:32.800 [main] INFO  net.armcloud.paascenter.traffic.info.service.impl.PadTrafficInfoServiceImpl -| 初始化磁盘信息缓冲区, batchSize=10, flushInterval=5s, capacity=1000
[] paas-center-traffic-info 2025-07-29 14:40:32.827 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| Started PaasCenterTrafficInfoApplication in 16.219 seconds (JVM running for 17.23)
[] paas-center-traffic-info 2025-07-29 14:40:32.840 [main] DEBUG com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner -|   ...  DDL start create  ...  
[] paas-center-traffic-info 2025-07-29 14:40:32.841 [main] DEBUG com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner -|   ...  DDL end create  ...  
[] paas-center-traffic-info 2025-07-29 14:40:32.841 [main] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initializing rocketmq consumer...
[] paas-center-traffic-info 2025-07-29 14:40:32.842 [rocketMqConsumer-1-t-3] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic pad_system_config_data 
[] paas-center-traffic-info 2025-07-29 14:40:32.842 [rocketMqConsumer-1-t-1] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic cbs_traffic_disk_data 
[] paas-center-traffic-info 2025-07-29 14:40:32.842 [rocketMqConsumer-1-t-2] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic device_system_config_data 
[] paas-center-traffic-info 2025-07-29 14:40:32.842 [rocketMqConsumer-1-t-5] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic pad_traffic_info_data 
[] paas-center-traffic-info 2025-07-29 14:40:32.842 [rocketMqConsumer-1-t-4] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic pad_traffic_data 
[] paas-center-traffic-info 2025-07-29 14:40:32.938 [main] INFO  com.alibaba.cloud.nacos.refresh.NacosContextRefresher -| [Nacos Config] Listening config: dataId=paas-center-traffic-info-docker.yaml, group=armcloud-paas-docker
[] paas-center-traffic-info 2025-07-29 14:40:33.527 [RMI TCP Connection(5)-************] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
[] paas-center-traffic-info 2025-07-29 14:40:33.528 [RMI TCP Connection(5)-************] INFO  org.springframework.web.servlet.DispatcherServlet -| Initializing Servlet 'dispatcherServlet'
[] paas-center-traffic-info 2025-07-29 14:40:33.545 [RMI TCP Connection(5)-************] INFO  org.springframework.web.servlet.DispatcherServlet -| Completed initialization in 17 ms
[] paas-center-traffic-info 2025-07-29 14:40:33.637 [RMI TCP Connection(4)-************] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[] paas-center-traffic-info 2025-07-29 14:40:33.637 [RMI TCP Connection(4)-************] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[] paas-center-traffic-info 2025-07-29 14:40:33.909 [RMI TCP Connection(4)-************] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-2 - Starting...
[] paas-center-traffic-info 2025-07-29 14:40:37.355 [RMI TCP Connection(4)-************] ERROR com.zaxxer.hikari.pool.HikariPool -| HikariPool-2 - Exception during pool initialization.
java.sql.SQLException: Connection refused: connect, server ClickHouseNode [uri=http://localhost:8123/armcloud, options={distributed_connections_pool_size=20,compress=1,max_parallel_replicas=2,keep_alive_timeout=30000,data_transfer_timeout=120000,max_partitions_per_insert_block=100,load_balancing=roundrobin,socket_timeout=60000,connection_timeout=30000}]@737055315
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:85)
	at com.clickhouse.jdbc.SqlExceptionUtils.create(SqlExceptionUtils.java:31)
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:90)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:131)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:335)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:288)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:157)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:41)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:330)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.getProduct(DataSourceHealthIndicator.java:122)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doDataSourceHealthCheck(DataSourceHealthIndicator.java:105)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doHealthCheck(DataSourceHealthIndicator.java:100)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:122)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468)
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76)
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309)
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401)
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at sun.reflect.GeneratedMethodAccessor102.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357)
	at sun.rmi.transport.Transport$1.run(Transport.java:200)
	at sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1340)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1315)
	at com.clickhouse.client.http.HttpUrlConnectionImpl.post(HttpUrlConnectionImpl.java:225)
	at com.clickhouse.client.http.ClickHouseHttpClient.send(ClickHouseHttpClient.java:124)
	at com.clickhouse.client.AbstractClient.execute(AbstractClient.java:280)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.sendOnce(ClickHouseClientBuilder.java:282)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.send(ClickHouseClientBuilder.java:294)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.execute(ClickHouseClientBuilder.java:349)
	at com.clickhouse.client.ClickHouseClient.executeAndWait(ClickHouseClient.java:1056)
	at com.clickhouse.client.ClickHouseRequest.executeAndWait(ClickHouseRequest.java:2154)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:128)
	... 64 common frames omitted
[] paas-center-traffic-info 2025-07-29 14:40:37.361 [RMI TCP Connection(4)-************] WARN  org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator -| DataSource health check failed
org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException: Connection refused: connect, server ClickHouseNode [uri=http://localhost:8123/armcloud, options={distributed_connections_pool_size=20,compress=1,max_parallel_replicas=2,keep_alive_timeout=30000,data_transfer_timeout=120000,max_partitions_per_insert_block=100,load_balancing=roundrobin,socket_timeout=60000,connection_timeout=30000}]@737055315
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:83)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:330)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.getProduct(DataSourceHealthIndicator.java:122)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doDataSourceHealthCheck(DataSourceHealthIndicator.java:105)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doHealthCheck(DataSourceHealthIndicator.java:100)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:122)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468)
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76)
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309)
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401)
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at sun.reflect.GeneratedMethodAccessor102.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357)
	at sun.rmi.transport.Transport$1.run(Transport.java:200)
	at sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLException: Connection refused: connect, server ClickHouseNode [uri=http://localhost:8123/armcloud, options={distributed_connections_pool_size=20,compress=1,max_parallel_replicas=2,keep_alive_timeout=30000,data_transfer_timeout=120000,max_partitions_per_insert_block=100,load_balancing=roundrobin,socket_timeout=60000,connection_timeout=30000}]@737055315
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:85)
	at com.clickhouse.jdbc.SqlExceptionUtils.create(SqlExceptionUtils.java:31)
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:90)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:131)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:335)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:288)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:157)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:41)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	... 50 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1340)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1315)
	at com.clickhouse.client.http.HttpUrlConnectionImpl.post(HttpUrlConnectionImpl.java:225)
	at com.clickhouse.client.http.ClickHouseHttpClient.send(ClickHouseHttpClient.java:124)
	at com.clickhouse.client.AbstractClient.execute(AbstractClient.java:280)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.sendOnce(ClickHouseClientBuilder.java:282)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.send(ClickHouseClientBuilder.java:294)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.execute(ClickHouseClientBuilder.java:349)
	at com.clickhouse.client.ClickHouseClient.executeAndWait(ClickHouseClient.java:1056)
	at com.clickhouse.client.ClickHouseRequest.executeAndWait(ClickHouseRequest.java:2154)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:128)
	... 64 common frames omitted
[] paas-center-traffic-info 2025-07-29 14:40:37.362 [RMI TCP Connection(4)-************] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Starting...
[] paas-center-traffic-info 2025-07-29 14:40:37.367 [RMI TCP Connection(4)-************] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Start completed.
[] paas-center-traffic-info 2025-07-29 14:41:00.707 [http-nio-18190-exec-1] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| ceph压力数据上报:{"deviceType":"CBS","clusterCode":"001","metrics":{"cephPressure":"32.12"}}
[] paas-center-traffic-info 2025-07-29 14:41:01.076 [http-nio-18190-exec-1] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 实时聚合分钟数据: clusterCode=001, pressure=32.12, aggregateResult=1
[] paas-center-traffic-info 2025-07-29 14:41:01.076 [http-nio-18190-exec-1] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 保存Ceph压力数据成功: clusterCode=001, pressure=32.12
[] paas-center-traffic-info 2025-07-29 14:41:01.078 [http-nio-18190-exec-1] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| Ceph压力数据保存成功: clusterCode=001
[] paas-center-traffic-info 2025-07-29 14:42:10.625 [http-nio-18190-exec-5] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| ceph压力数据上报:{"deviceType":"CBS","clusterCode":"001","metrics":{"cephPressure":"55.12"}}
[] paas-center-traffic-info 2025-07-29 14:42:10.650 [http-nio-18190-exec-5] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 实时聚合分钟数据: clusterCode=001, pressure=55.12, aggregateResult=1
[] paas-center-traffic-info 2025-07-29 14:42:10.650 [http-nio-18190-exec-5] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 保存Ceph压力数据成功: clusterCode=001, pressure=55.12
[] paas-center-traffic-info 2025-07-29 14:42:10.652 [http-nio-18190-exec-5] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| Ceph压力数据保存成功: clusterCode=001
[] paas-center-traffic-info 2025-07-29 14:42:13.412 [http-nio-18190-exec-6] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| ceph压力数据上报:{"deviceType":"CBS","clusterCode":"001","metrics":{"cephPressure":"79.12"}}
[] paas-center-traffic-info 2025-07-29 14:42:13.451 [http-nio-18190-exec-6] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 实时聚合分钟数据: clusterCode=001, pressure=79.12, aggregateResult=2
[] paas-center-traffic-info 2025-07-29 14:42:13.452 [http-nio-18190-exec-6] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 保存Ceph压力数据成功: clusterCode=001, pressure=79.12
[] paas-center-traffic-info 2025-07-29 14:42:13.454 [http-nio-18190-exec-6] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| Ceph压力数据保存成功: clusterCode=001
[] paas-center-traffic-info 2025-07-29 14:42:15.747 [http-nio-18190-exec-8] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| ceph压力数据上报:{"deviceType":"CBS","clusterCode":"001","metrics":{"cephPressure":"30.12"}}
[] paas-center-traffic-info 2025-07-29 14:42:15.776 [http-nio-18190-exec-8] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 实时聚合分钟数据: clusterCode=001, pressure=30.12, aggregateResult=2
[] paas-center-traffic-info 2025-07-29 14:42:15.776 [http-nio-18190-exec-8] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 保存Ceph压力数据成功: clusterCode=001, pressure=30.12
[] paas-center-traffic-info 2025-07-29 14:42:15.777 [http-nio-18190-exec-8] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| Ceph压力数据保存成功: clusterCode=001
[] paas-center-traffic-info 2025-07-29 14:44:03.965 [Thread-10] WARN  com.alibaba.nacos.common.notify.NotifyCenter -| [NotifyCenter] Start destroying Publisher
[] paas-center-traffic-info 2025-07-29 14:44:03.966 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder -| [HttpClientBeanHolder] Start destroying common HttpClient
[] paas-center-traffic-info 2025-07-29 14:44:03.966 [Thread-10] WARN  com.alibaba.nacos.common.notify.NotifyCenter -| [NotifyCenter] Destruction of the end
[] paas-center-traffic-info 2025-07-29 14:44:03.966 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder -| [HttpClientBeanHolder] Destruction of the end
[] paas-center-traffic-info 2025-07-29 14:44:04.163 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoSendMsgService -| 关闭缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 14:44:07.291 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Shutdown initiated...
[] paas-center-traffic-info 2025-07-29 14:44:07.306 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Shutdown completed.
[] paas-center-traffic-info 2025-07-29 14:44:07.306 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoClickHouseService -| 关闭ClickHouse磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 14:44:07.306 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Shutdown initiated...
[] paas-center-traffic-info 2025-07-29 14:44:07.313 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Shutdown completed.
[] paas-center-traffic-info 2025-07-29 14:44:07.314 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.impl.DiskInfoServiceImpl -| 关闭ClickHouse磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 14:44:07.314 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.impl.PadTrafficInfoServiceImpl -| 关闭磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 14:44:14.734 [background-preinit] INFO  org.hibernate.validator.internal.util.Version -| HV000001: Hibernate Validator 6.2.5.Final
[] paas-center-traffic-info 2025-07-29 14:44:14.760 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| Starting PaasCenterTrafficInfoApplication using Java 1.8.0_252 on DESKTOP-D51FIJ4 with PID 32508 (D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes started by xskj in D:\dev\workspace\paas-center-traffic-info)
[] paas-center-traffic-info 2025-07-29 14:44:14.760 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| The following 1 profile is active: "docker"
[] paas-center-traffic-info 2025-07-29 14:44:14.853 [main] INFO  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader -| [Nacos Config] Load config[dataId=paas-center-traffic-info-docker.yaml, group=armcloud-paas-docker] success
[] paas-center-traffic-info 2025-07-29 14:44:15.831 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Multiple Spring Data modules found, entering strict repository configuration mode
[] paas-center-traffic-info 2025-07-29 14:44:15.834 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[] paas-center-traffic-info 2025-07-29 14:44:15.858 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
[] paas-center-traffic-info 2025-07-29 14:44:16.137 [main] INFO  org.springframework.cloud.context.scope.GenericScope -| BeanFactory id=24e5d0b6-9883-366d-a264-af4bafc3a277
[] paas-center-traffic-info 2025-07-29 14:44:16.417 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$EnhancerBySpringCGLIB$$c3cf6a1f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:44:16.421 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$EnhancerBySpringCGLIB$$6c957383] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:44:16.554 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:44:16.562 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:44:16.563 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:44:16.563 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$537/285301920] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:44:16.564 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:44:16.571 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:44:16.576 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:44:16.871 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer -| Tomcat initialized with port(s): 18190 (http)
[] paas-center-traffic-info 2025-07-29 14:44:16.884 [main] INFO  org.apache.coyote.http11.Http11NioProtocol -| Initializing ProtocolHandler ["http-nio-18190"]
[] paas-center-traffic-info 2025-07-29 14:44:16.885 [main] INFO  org.apache.catalina.core.StandardService -| Starting service [Tomcat]
[] paas-center-traffic-info 2025-07-29 14:44:16.885 [main] INFO  org.apache.catalina.core.StandardEngine -| Starting Servlet engine: [Apache Tomcat/9.0.68]
[] paas-center-traffic-info 2025-07-29 14:44:17.041 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] -| Initializing Spring embedded WebApplicationContext
[] paas-center-traffic-info 2025-07-29 14:44:17.041 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext -| Root WebApplicationContext: initialization completed in 2183 ms
[] paas-center-traffic-info 2025-07-29 14:44:17.323 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 初始化ClickHouse数据源...
[] paas-center-traffic-info 2025-07-29 14:44:17.323 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 成功加载 ClickHouse 驱动类
[] paas-center-traffic-info 2025-07-29 14:44:17.323 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| ClickHouse DataSource bean created successfully
[] paas-center-traffic-info 2025-07-29 14:44:17.327 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 创建ClickHouse SQLSessionFactory, 数据源类型: com.zaxxer.hikari.HikariDataSource
[] paas-center-traffic-info 2025-07-29 14:44:17.552 [main] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoClickHouseService -| 初始化ClickHouse磁盘信息缓冲区, batchSize=100, flushInterval=10s, capacity=10000
[] paas-center-traffic-info 2025-07-29 14:44:17.591 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Property 'configuration' or 'configLocation' not specified, using default MyBatis Configuration
[] paas-center-traffic-info 2025-07-29 14:44:17.739 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\CephPressureDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:44:17.756 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\NetStoragePadUnitDetailMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:44:17.872 [main] DEBUG com.baomidou.mybatisplus.core.toolkit.Sequence -| Initialization Sequence datacenterId:0 workerId:14
[] paas-center-traffic-info 2025-07-29 14:44:18.319 [main] INFO  org.redisson.Version -| Redisson 3.17.2
[] paas-center-traffic-info 2025-07-29 14:44:19.077 [redisson-netty-2-13] INFO  org.redisson.connection.pool.MasterPubSubConnectionPool -| 1 connections initialized for **************/**************:6379
[] paas-center-traffic-info 2025-07-29 14:44:19.097 [redisson-netty-2-19] INFO  org.redisson.connection.pool.MasterConnectionPool -| 24 connections initialized for **************/**************:6379
[] paas-center-traffic-info 2025-07-29 14:44:19.409 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Property 'configuration' or 'configLocation' not specified, using default MyBatis Configuration
[] paas-center-traffic-info 2025-07-29 14:44:19.415 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\traffic\DeviceSystemConfigDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:44:19.419 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\traffic\PadSystemConfigDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:44:19.422 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\traffic\PadTrafficInfoMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:44:19.532 [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean -| For 'paas-center-core' URL not provided. Will try picking an instance via load-balancing.
[] paas-center-traffic-info 2025-07-29 14:44:19.714 [main] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Starting...
[] paas-center-traffic-info 2025-07-29 14:44:20.009 [main] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Start completed.
[] paas-center-traffic-info 2025-07-29 14:44:20.322 [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean -| For 'paas-center-core' URL not provided. Will try picking an instance via load-balancing.
[] paas-center-traffic-info 2025-07-29 14:44:21.412 [main] INFO  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration -| a producer (test_topic_producer_ali_sdk) init on namesrv **************:9876
[] paas-center-traffic-info 2025-07-29 14:44:22.666 [main] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingProducer -| 》》》》  Initialized loading RocketMQ producer Success 》》》》
[] paas-center-traffic-info 2025-07-29 14:44:24.322 [main] INFO  org.springframework.cloud.commons.util.InetUtils -| Cannot determine local hostname
[] paas-center-traffic-info 2025-07-29 14:44:26.525 [main] INFO  org.springframework.cloud.commons.util.InetUtils -| Cannot determine local hostname
[] paas-center-traffic-info 2025-07-29 14:44:26.660 [main] WARN  org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger -| Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[] paas-center-traffic-info 2025-07-29 14:44:26.669 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver -| Exposing 2 endpoint(s) beneath base path '/actuator'
[] paas-center-traffic-info 2025-07-29 14:44:26.761 [main] INFO  org.apache.coyote.http11.Http11NioProtocol -| Starting ProtocolHandler ["http-nio-18190"]
[] paas-center-traffic-info 2025-07-29 14:44:26.788 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer -| Tomcat started on port(s): 18190 (http) with context path ''
[] paas-center-traffic-info 2025-07-29 14:44:27.946 [main] INFO  org.springframework.cloud.commons.util.InetUtils -| Cannot determine local hostname
[] paas-center-traffic-info 2025-07-29 14:44:27.968 [main] INFO  net.armcloud.paascenter.traffic.info.service.impl.DiskInfoServiceImpl -| 初始化ClickHouse磁盘信息缓冲区, batchSize=1000, flushInterval=2s, capacity=10000
[] paas-center-traffic-info 2025-07-29 14:44:28.041 [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean -| For 'paas-center-core' URL not provided. Will try picking an instance via load-balancing.
[] paas-center-traffic-info 2025-07-29 14:44:28.050 [main] INFO  net.armcloud.paascenter.traffic.info.service.impl.PadTrafficInfoServiceImpl -| 初始化磁盘信息缓冲区, batchSize=10, flushInterval=5s, capacity=1000
[] paas-center-traffic-info 2025-07-29 14:44:28.077 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| Started PaasCenterTrafficInfoApplication in 16.581 seconds (JVM running for 17.455)
[] paas-center-traffic-info 2025-07-29 14:44:28.091 [main] DEBUG com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner -|   ...  DDL start create  ...  
[] paas-center-traffic-info 2025-07-29 14:44:28.092 [main] DEBUG com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner -|   ...  DDL end create  ...  
[] paas-center-traffic-info 2025-07-29 14:44:28.092 [main] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initializing rocketmq consumer...
[] paas-center-traffic-info 2025-07-29 14:44:28.094 [rocketMqConsumer-1-t-2] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic device_system_config_data 
[] paas-center-traffic-info 2025-07-29 14:44:28.094 [rocketMqConsumer-1-t-1] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic cbs_traffic_disk_data 
[] paas-center-traffic-info 2025-07-29 14:44:28.094 [rocketMqConsumer-1-t-5] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic pad_traffic_info_data 
[] paas-center-traffic-info 2025-07-29 14:44:28.094 [rocketMqConsumer-1-t-4] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic pad_traffic_data 
[] paas-center-traffic-info 2025-07-29 14:44:28.095 [rocketMqConsumer-1-t-3] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic pad_system_config_data 
[] paas-center-traffic-info 2025-07-29 14:44:28.193 [main] INFO  com.alibaba.cloud.nacos.refresh.NacosContextRefresher -| [Nacos Config] Listening config: dataId=paas-center-traffic-info-docker.yaml, group=armcloud-paas-docker
[] paas-center-traffic-info 2025-07-29 14:44:28.525 [RMI TCP Connection(4)-************] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
[] paas-center-traffic-info 2025-07-29 14:44:28.526 [RMI TCP Connection(4)-************] INFO  org.springframework.web.servlet.DispatcherServlet -| Initializing Servlet 'dispatcherServlet'
[] paas-center-traffic-info 2025-07-29 14:44:28.550 [RMI TCP Connection(4)-************] INFO  org.springframework.web.servlet.DispatcherServlet -| Completed initialization in 24 ms
[] paas-center-traffic-info 2025-07-29 14:44:28.629 [RMI TCP Connection(5)-************] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[] paas-center-traffic-info 2025-07-29 14:44:28.629 [RMI TCP Connection(5)-************] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[] paas-center-traffic-info 2025-07-29 14:44:28.910 [RMI TCP Connection(5)-************] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-2 - Starting...
[] paas-center-traffic-info 2025-07-29 14:44:32.386 [RMI TCP Connection(5)-************] ERROR com.zaxxer.hikari.pool.HikariPool -| HikariPool-2 - Exception during pool initialization.
java.sql.SQLException: Connection refused: connect, server ClickHouseNode [uri=http://localhost:8123/armcloud, options={distributed_connections_pool_size=20,compress=1,max_parallel_replicas=2,keep_alive_timeout=30000,data_transfer_timeout=120000,max_partitions_per_insert_block=100,load_balancing=roundrobin,socket_timeout=60000,connection_timeout=30000}]@-280771091
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:85)
	at com.clickhouse.jdbc.SqlExceptionUtils.create(SqlExceptionUtils.java:31)
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:90)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:131)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:335)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:288)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:157)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:41)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:330)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.getProduct(DataSourceHealthIndicator.java:122)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doDataSourceHealthCheck(DataSourceHealthIndicator.java:105)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doHealthCheck(DataSourceHealthIndicator.java:100)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:122)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468)
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76)
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309)
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401)
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at sun.reflect.GeneratedMethodAccessor99.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357)
	at sun.rmi.transport.Transport$1.run(Transport.java:200)
	at sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1340)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1315)
	at com.clickhouse.client.http.HttpUrlConnectionImpl.post(HttpUrlConnectionImpl.java:225)
	at com.clickhouse.client.http.ClickHouseHttpClient.send(ClickHouseHttpClient.java:124)
	at com.clickhouse.client.AbstractClient.execute(AbstractClient.java:280)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.sendOnce(ClickHouseClientBuilder.java:282)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.send(ClickHouseClientBuilder.java:294)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.execute(ClickHouseClientBuilder.java:349)
	at com.clickhouse.client.ClickHouseClient.executeAndWait(ClickHouseClient.java:1056)
	at com.clickhouse.client.ClickHouseRequest.executeAndWait(ClickHouseRequest.java:2154)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:128)
	... 64 common frames omitted
[] paas-center-traffic-info 2025-07-29 14:44:32.390 [RMI TCP Connection(5)-************] WARN  org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator -| DataSource health check failed
org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException: Connection refused: connect, server ClickHouseNode [uri=http://localhost:8123/armcloud, options={distributed_connections_pool_size=20,compress=1,max_parallel_replicas=2,keep_alive_timeout=30000,data_transfer_timeout=120000,max_partitions_per_insert_block=100,load_balancing=roundrobin,socket_timeout=60000,connection_timeout=30000}]@-280771091
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:83)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:330)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.getProduct(DataSourceHealthIndicator.java:122)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doDataSourceHealthCheck(DataSourceHealthIndicator.java:105)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doHealthCheck(DataSourceHealthIndicator.java:100)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:122)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468)
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76)
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309)
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401)
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at sun.reflect.GeneratedMethodAccessor99.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357)
	at sun.rmi.transport.Transport$1.run(Transport.java:200)
	at sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLException: Connection refused: connect, server ClickHouseNode [uri=http://localhost:8123/armcloud, options={distributed_connections_pool_size=20,compress=1,max_parallel_replicas=2,keep_alive_timeout=30000,data_transfer_timeout=120000,max_partitions_per_insert_block=100,load_balancing=roundrobin,socket_timeout=60000,connection_timeout=30000}]@-280771091
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:85)
	at com.clickhouse.jdbc.SqlExceptionUtils.create(SqlExceptionUtils.java:31)
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:90)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:131)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:335)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:288)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:157)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:41)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	... 50 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1340)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1315)
	at com.clickhouse.client.http.HttpUrlConnectionImpl.post(HttpUrlConnectionImpl.java:225)
	at com.clickhouse.client.http.ClickHouseHttpClient.send(ClickHouseHttpClient.java:124)
	at com.clickhouse.client.AbstractClient.execute(AbstractClient.java:280)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.sendOnce(ClickHouseClientBuilder.java:282)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.send(ClickHouseClientBuilder.java:294)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.execute(ClickHouseClientBuilder.java:349)
	at com.clickhouse.client.ClickHouseClient.executeAndWait(ClickHouseClient.java:1056)
	at com.clickhouse.client.ClickHouseRequest.executeAndWait(ClickHouseRequest.java:2154)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:128)
	... 64 common frames omitted
[] paas-center-traffic-info 2025-07-29 14:44:32.391 [RMI TCP Connection(5)-************] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Starting...
[] paas-center-traffic-info 2025-07-29 14:44:32.397 [RMI TCP Connection(5)-************] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Start completed.
[] paas-center-traffic-info 2025-07-29 14:44:42.135 [http-nio-18190-exec-5] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| ceph压力数据上报:{"deviceType":"CBS","clusterCode":"001","metrics":{"cephPressure":"30.12"}}
[] paas-center-traffic-info 2025-07-29 14:44:42.521 [http-nio-18190-exec-5] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 实时聚合分钟数据: clusterCode=001, pressure=30.12, aggregateResult=1
[] paas-center-traffic-info 2025-07-29 14:44:42.521 [http-nio-18190-exec-5] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 保存Ceph压力数据成功: clusterCode=001, pressure=30.12
[] paas-center-traffic-info 2025-07-29 14:44:42.523 [http-nio-18190-exec-5] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| Ceph压力数据保存成功: clusterCode=001
[] paas-center-traffic-info 2025-07-29 14:44:48.489 [http-nio-18190-exec-3] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| ceph压力数据上报:{"deviceType":"CBS","clusterCode":"001","metrics":{"cephPressure":"76.12"}}
[] paas-center-traffic-info 2025-07-29 14:44:48.780 [http-nio-18190-exec-3] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 实时聚合分钟数据: clusterCode=001, pressure=76.12, aggregateResult=2
[] paas-center-traffic-info 2025-07-29 14:44:48.780 [http-nio-18190-exec-3] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 保存Ceph压力数据成功: clusterCode=001, pressure=76.12
[] paas-center-traffic-info 2025-07-29 14:44:48.782 [http-nio-18190-exec-3] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| Ceph压力数据保存成功: clusterCode=001
[] paas-center-traffic-info 2025-07-29 14:44:50.264 [http-nio-18190-exec-4] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| ceph压力数据上报:{"deviceType":"CBS","clusterCode":"001","metrics":{"cephPressure":"13.12"}}
[] paas-center-traffic-info 2025-07-29 14:44:50.278 [http-nio-18190-exec-4] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 实时聚合分钟数据: clusterCode=001, pressure=13.12, aggregateResult=2
[] paas-center-traffic-info 2025-07-29 14:44:50.278 [http-nio-18190-exec-4] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 保存Ceph压力数据成功: clusterCode=001, pressure=13.12
[] paas-center-traffic-info 2025-07-29 14:44:50.280 [http-nio-18190-exec-4] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| Ceph压力数据保存成功: clusterCode=001
[] paas-center-traffic-info 2025-07-29 14:44:54.098 [http-nio-18190-exec-6] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| ceph压力数据上报:{"deviceType":"CBS","clusterCode":"001","metrics":{"cephPressure":"20.12"}}
[] paas-center-traffic-info 2025-07-29 14:44:54.113 [http-nio-18190-exec-6] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 实时聚合分钟数据: clusterCode=001, pressure=20.12, aggregateResult=2
[] paas-center-traffic-info 2025-07-29 14:44:54.113 [http-nio-18190-exec-6] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 保存Ceph压力数据成功: clusterCode=001, pressure=20.12
[] paas-center-traffic-info 2025-07-29 14:44:54.113 [http-nio-18190-exec-6] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| Ceph压力数据保存成功: clusterCode=001
[] paas-center-traffic-info 2025-07-29 14:45:05.684 [http-nio-18190-exec-2] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| ceph压力数据上报:{"deviceType":"CBS","clusterCode":"001","metrics":{"cephPressure":"22.12"}}
[] paas-center-traffic-info 2025-07-29 14:45:05.701 [http-nio-18190-exec-2] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 实时聚合分钟数据: clusterCode=001, pressure=22.12, aggregateResult=1
[] paas-center-traffic-info 2025-07-29 14:45:05.701 [http-nio-18190-exec-2] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 保存Ceph压力数据成功: clusterCode=001, pressure=22.12
[] paas-center-traffic-info 2025-07-29 14:45:05.704 [http-nio-18190-exec-2] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| Ceph压力数据保存成功: clusterCode=001
[] paas-center-traffic-info 2025-07-29 14:45:07.666 [http-nio-18190-exec-7] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| ceph压力数据上报:{"deviceType":"CBS","clusterCode":"001","metrics":{"cephPressure":"53.12"}}
[] paas-center-traffic-info 2025-07-29 14:45:07.682 [http-nio-18190-exec-7] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 实时聚合分钟数据: clusterCode=001, pressure=53.12, aggregateResult=2
[] paas-center-traffic-info 2025-07-29 14:45:07.684 [http-nio-18190-exec-7] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 保存Ceph压力数据成功: clusterCode=001, pressure=53.12
[] paas-center-traffic-info 2025-07-29 14:45:07.685 [http-nio-18190-exec-7] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| Ceph压力数据保存成功: clusterCode=001
[] paas-center-traffic-info 2025-07-29 14:45:09.465 [http-nio-18190-exec-8] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| ceph压力数据上报:{"deviceType":"CBS","clusterCode":"001","metrics":{"cephPressure":"63.12"}}
[] paas-center-traffic-info 2025-07-29 14:45:09.499 [http-nio-18190-exec-8] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 实时聚合分钟数据: clusterCode=001, pressure=63.12, aggregateResult=2
[] paas-center-traffic-info 2025-07-29 14:45:09.499 [http-nio-18190-exec-8] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 保存Ceph压力数据成功: clusterCode=001, pressure=63.12
[] paas-center-traffic-info 2025-07-29 14:45:09.501 [http-nio-18190-exec-8] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| Ceph压力数据保存成功: clusterCode=001
[] paas-center-traffic-info 2025-07-29 14:45:11.948 [http-nio-18190-exec-9] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| ceph压力数据上报:{"deviceType":"CBS","clusterCode":"001","metrics":{"cephPressure":"20.12"}}
[] paas-center-traffic-info 2025-07-29 14:45:11.963 [http-nio-18190-exec-9] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 实时聚合分钟数据: clusterCode=001, pressure=20.12, aggregateResult=2
[] paas-center-traffic-info 2025-07-29 14:45:11.963 [http-nio-18190-exec-9] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 保存Ceph压力数据成功: clusterCode=001, pressure=20.12
[] paas-center-traffic-info 2025-07-29 14:45:11.964 [http-nio-18190-exec-9] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| Ceph压力数据保存成功: clusterCode=001
[] paas-center-traffic-info 2025-07-29 14:45:14.357 [http-nio-18190-exec-10] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| ceph压力数据上报:{"deviceType":"CBS","clusterCode":"001","metrics":{"cephPressure":"26.12"}}
[] paas-center-traffic-info 2025-07-29 14:45:18.251 [http-nio-18190-exec-10] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 实时聚合分钟数据: clusterCode=001, pressure=26.12, aggregateResult=2
[] paas-center-traffic-info 2025-07-29 14:45:18.251 [http-nio-18190-exec-10] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 保存Ceph压力数据成功: clusterCode=001, pressure=26.12
[] paas-center-traffic-info 2025-07-29 14:45:18.253 [http-nio-18190-exec-10] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| Ceph压力数据保存成功: clusterCode=001
[] paas-center-traffic-info 2025-07-29 14:45:19.958 [http-nio-18190-exec-1] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| ceph压力数据上报:{"deviceType":"CBS","clusterCode":"001","metrics":{"cephPressure":"13.12"}}
[] paas-center-traffic-info 2025-07-29 14:45:19.982 [http-nio-18190-exec-1] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 实时聚合分钟数据: clusterCode=001, pressure=13.12, aggregateResult=2
[] paas-center-traffic-info 2025-07-29 14:45:19.982 [http-nio-18190-exec-1] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 保存Ceph压力数据成功: clusterCode=001, pressure=13.12
[] paas-center-traffic-info 2025-07-29 14:45:19.983 [http-nio-18190-exec-1] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| Ceph压力数据保存成功: clusterCode=001
[] paas-center-traffic-info 2025-07-29 14:45:22.762 [http-nio-18190-exec-5] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| ceph压力数据上报:{"deviceType":"CBS","clusterCode":"001","metrics":{"cephPressure":"11.12"}}
[] paas-center-traffic-info 2025-07-29 14:45:25.207 [http-nio-18190-exec-5] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 实时聚合分钟数据: clusterCode=001, pressure=11.12, aggregateResult=2
[] paas-center-traffic-info 2025-07-29 14:45:25.207 [http-nio-18190-exec-5] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 保存Ceph压力数据成功: clusterCode=001, pressure=11.12
[] paas-center-traffic-info 2025-07-29 14:45:25.211 [http-nio-18190-exec-5] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| Ceph压力数据保存成功: clusterCode=001
[] paas-center-traffic-info 2025-07-29 14:45:52.384 [http-nio-18190-exec-3] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| ceph压力数据上报:{"deviceType":"CBS","clusterCode":"001","metrics":{"cephPressure":"11.12"}}
[] paas-center-traffic-info 2025-07-29 14:45:52.400 [http-nio-18190-exec-3] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 实时聚合分钟数据: clusterCode=001, pressure=11.12, aggregateResult=2
[] paas-center-traffic-info 2025-07-29 14:45:52.401 [http-nio-18190-exec-3] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 保存Ceph压力数据成功: clusterCode=001, pressure=11.12
[] paas-center-traffic-info 2025-07-29 14:45:52.430 [http-nio-18190-exec-3] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| Ceph压力数据保存成功: clusterCode=001
[] paas-center-traffic-info 2025-07-29 14:45:56.749 [http-nio-18190-exec-4] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| ceph压力数据上报:{"deviceType":"CBS","clusterCode":"001","metrics":{"cephPressure":"90.12"}}
[] paas-center-traffic-info 2025-07-29 14:45:56.759 [http-nio-18190-exec-4] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 实时聚合分钟数据: clusterCode=001, pressure=90.12, aggregateResult=2
[] paas-center-traffic-info 2025-07-29 14:45:56.760 [http-nio-18190-exec-4] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 保存Ceph压力数据成功: clusterCode=001, pressure=90.12
[] paas-center-traffic-info 2025-07-29 14:45:56.761 [http-nio-18190-exec-4] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| Ceph压力数据保存成功: clusterCode=001
[] paas-center-traffic-info 2025-07-29 14:45:59.936 [http-nio-18190-exec-6] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| ceph压力数据上报:{"deviceType":"CBS","clusterCode":"001","metrics":{"cephPressure":"74.12"}}
[] paas-center-traffic-info 2025-07-29 14:45:59.947 [http-nio-18190-exec-6] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 实时聚合分钟数据: clusterCode=001, pressure=74.12, aggregateResult=2
[] paas-center-traffic-info 2025-07-29 14:45:59.948 [http-nio-18190-exec-6] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 保存Ceph压力数据成功: clusterCode=001, pressure=74.12
[] paas-center-traffic-info 2025-07-29 14:45:59.949 [http-nio-18190-exec-6] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| Ceph压力数据保存成功: clusterCode=001
[] paas-center-traffic-info 2025-07-29 14:46:01.918 [http-nio-18190-exec-2] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| ceph压力数据上报:{"deviceType":"CBS","clusterCode":"001","metrics":{"cephPressure":"27.12"}}
[] paas-center-traffic-info 2025-07-29 14:46:01.928 [http-nio-18190-exec-2] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 实时聚合分钟数据: clusterCode=001, pressure=27.12, aggregateResult=1
[] paas-center-traffic-info 2025-07-29 14:46:01.928 [http-nio-18190-exec-2] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 保存Ceph压力数据成功: clusterCode=001, pressure=27.12
[] paas-center-traffic-info 2025-07-29 14:46:01.930 [http-nio-18190-exec-2] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| Ceph压力数据保存成功: clusterCode=001
[] paas-center-traffic-info 2025-07-29 14:46:03.747 [http-nio-18190-exec-7] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| ceph压力数据上报:{"deviceType":"CBS","clusterCode":"001","metrics":{"cephPressure":"84.12"}}
[] paas-center-traffic-info 2025-07-29 14:46:03.760 [http-nio-18190-exec-7] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 实时聚合分钟数据: clusterCode=001, pressure=84.12, aggregateResult=2
[] paas-center-traffic-info 2025-07-29 14:46:03.760 [http-nio-18190-exec-7] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 保存Ceph压力数据成功: clusterCode=001, pressure=84.12
[] paas-center-traffic-info 2025-07-29 14:46:03.761 [http-nio-18190-exec-7] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| Ceph压力数据保存成功: clusterCode=001
[] paas-center-traffic-info 2025-07-29 14:49:39.661 [http-nio-18190-exec-10] WARN  org.springframework.web.servlet.mvc.support.DefaultHandlerExceptionResolver -| Resolved [org.springframework.http.converter.HttpMessageNotReadableException: Required request body is missing: public net.armcloud.paascenter.traffic.info.domain.Result<java.util.List<net.armcloud.paascenter.traffic.info.model.dto.CephPressureChartDTO>> net.armcloud.paascenter.traffic.info.controller.TrafficDataController.getCephPressureChart(net.armcloud.paascenter.traffic.info.model.dto.CephPressureQueryDTO)]
[] paas-center-traffic-info 2025-07-29 14:49:43.673 [http-nio-18190-exec-5] WARN  org.springframework.web.servlet.mvc.support.DefaultHandlerExceptionResolver -| Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public net.armcloud.paascenter.traffic.info.domain.Result<java.util.List<net.armcloud.paascenter.traffic.info.model.dto.CephPressureChartDTO>> net.armcloud.paascenter.traffic.info.controller.TrafficDataController.getCephPressureChart(net.armcloud.paascenter.traffic.info.model.dto.CephPressureQueryDTO) with 2 errors: [Field error in object 'cephPressureQueryDTO' on field 'startTime': rejected value [null]; codes [NotNull.cephPressureQueryDTO.startTime,NotNull.startTime,NotNull.java.util.Date,NotNull]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [cephPressureQueryDTO.startTime,startTime]; arguments []; default message [startTime]]; default message [开始时间不能为空]] [Field error in object 'cephPressureQueryDTO' on field 'endTime': rejected value [null]; codes [NotNull.cephPressureQueryDTO.endTime,NotNull.endTime,NotNull.java.util.Date,NotNull]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [cephPressureQueryDTO.endTime,endTime]; arguments []; default message [endTime]]; default message [结束时间不能为空]] ]
[] paas-center-traffic-info 2025-07-29 14:50:43.423 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder -| [HttpClientBeanHolder] Start destroying common HttpClient
[] paas-center-traffic-info 2025-07-29 14:50:43.424 [Thread-10] WARN  com.alibaba.nacos.common.notify.NotifyCenter -| [NotifyCenter] Start destroying Publisher
[] paas-center-traffic-info 2025-07-29 14:50:43.424 [Thread-10] WARN  com.alibaba.nacos.common.notify.NotifyCenter -| [NotifyCenter] Destruction of the end
[] paas-center-traffic-info 2025-07-29 14:50:43.424 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder -| [HttpClientBeanHolder] Destruction of the end
[] paas-center-traffic-info 2025-07-29 14:50:43.622 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoSendMsgService -| 关闭缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 14:50:46.721 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Shutdown initiated...
[] paas-center-traffic-info 2025-07-29 14:50:46.735 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Shutdown completed.
[] paas-center-traffic-info 2025-07-29 14:50:46.735 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoClickHouseService -| 关闭ClickHouse磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 14:50:46.735 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Shutdown initiated...
[] paas-center-traffic-info 2025-07-29 14:50:46.745 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Shutdown completed.
[] paas-center-traffic-info 2025-07-29 14:50:46.746 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.impl.DiskInfoServiceImpl -| 关闭ClickHouse磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 14:50:46.746 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.impl.PadTrafficInfoServiceImpl -| 关闭磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 14:50:54.640 [background-preinit] INFO  org.hibernate.validator.internal.util.Version -| HV000001: Hibernate Validator 6.2.5.Final
[] paas-center-traffic-info 2025-07-29 14:50:54.667 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| Starting PaasCenterTrafficInfoApplication using Java 1.8.0_252 on DESKTOP-D51FIJ4 with PID 43888 (D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes started by xskj in D:\dev\workspace\paas-center-traffic-info)
[] paas-center-traffic-info 2025-07-29 14:50:54.667 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| The following 1 profile is active: "docker"
[] paas-center-traffic-info 2025-07-29 14:50:54.727 [main] INFO  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader -| [Nacos Config] Load config[dataId=paas-center-traffic-info-docker.yaml, group=armcloud-paas-docker] success
[] paas-center-traffic-info 2025-07-29 14:50:55.699 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Multiple Spring Data modules found, entering strict repository configuration mode
[] paas-center-traffic-info 2025-07-29 14:50:55.703 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[] paas-center-traffic-info 2025-07-29 14:50:55.726 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
[] paas-center-traffic-info 2025-07-29 14:50:55.996 [main] INFO  org.springframework.cloud.context.scope.GenericScope -| BeanFactory id=96fb6da4-8676-337b-a389-f874aef18e94
[] paas-center-traffic-info 2025-07-29 14:50:56.252 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$EnhancerBySpringCGLIB$$68823c95] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:50:56.256 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$EnhancerBySpringCGLIB$$114845f9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:50:56.372 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:50:56.379 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:50:56.381 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:50:56.381 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$537/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:50:56.382 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:50:56.389 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:50:56.392 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:50:56.689 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer -| Tomcat initialized with port(s): 18190 (http)
[] paas-center-traffic-info 2025-07-29 14:50:56.702 [main] INFO  org.apache.coyote.http11.Http11NioProtocol -| Initializing ProtocolHandler ["http-nio-18190"]
[] paas-center-traffic-info 2025-07-29 14:50:56.702 [main] INFO  org.apache.catalina.core.StandardService -| Starting service [Tomcat]
[] paas-center-traffic-info 2025-07-29 14:50:56.702 [main] INFO  org.apache.catalina.core.StandardEngine -| Starting Servlet engine: [Apache Tomcat/9.0.68]
[] paas-center-traffic-info 2025-07-29 14:50:56.876 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] -| Initializing Spring embedded WebApplicationContext
[] paas-center-traffic-info 2025-07-29 14:50:56.876 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext -| Root WebApplicationContext: initialization completed in 2145 ms
[] paas-center-traffic-info 2025-07-29 14:50:57.194 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 初始化ClickHouse数据源...
[] paas-center-traffic-info 2025-07-29 14:50:57.195 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 成功加载 ClickHouse 驱动类
[] paas-center-traffic-info 2025-07-29 14:50:57.195 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| ClickHouse DataSource bean created successfully
[] paas-center-traffic-info 2025-07-29 14:50:57.199 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 创建ClickHouse SQLSessionFactory, 数据源类型: com.zaxxer.hikari.HikariDataSource
[] paas-center-traffic-info 2025-07-29 14:50:57.429 [main] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoClickHouseService -| 初始化ClickHouse磁盘信息缓冲区, batchSize=100, flushInterval=10s, capacity=10000
[] paas-center-traffic-info 2025-07-29 14:50:57.462 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Property 'configuration' or 'configLocation' not specified, using default MyBatis Configuration
[] paas-center-traffic-info 2025-07-29 14:50:57.603 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\CephPressureDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:50:57.618 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\NetStoragePadUnitDetailMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:50:57.731 [main] DEBUG com.baomidou.mybatisplus.core.toolkit.Sequence -| Initialization Sequence datacenterId:0 workerId:9
[] paas-center-traffic-info 2025-07-29 14:50:58.142 [main] INFO  org.redisson.Version -| Redisson 3.17.2
[] paas-center-traffic-info 2025-07-29 14:50:58.889 [redisson-netty-2-12] INFO  org.redisson.connection.pool.MasterPubSubConnectionPool -| 1 connections initialized for **************/**************:6379
[] paas-center-traffic-info 2025-07-29 14:50:58.909 [redisson-netty-2-19] INFO  org.redisson.connection.pool.MasterConnectionPool -| 24 connections initialized for **************/**************:6379
[] paas-center-traffic-info 2025-07-29 14:50:59.198 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Property 'configuration' or 'configLocation' not specified, using default MyBatis Configuration
[] paas-center-traffic-info 2025-07-29 14:50:59.204 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\traffic\DeviceSystemConfigDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:50:59.208 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\traffic\PadSystemConfigDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:50:59.212 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\traffic\PadTrafficInfoMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:50:59.321 [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean -| For 'paas-center-core' URL not provided. Will try picking an instance via load-balancing.
[] paas-center-traffic-info 2025-07-29 14:50:59.487 [main] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Starting...
[] paas-center-traffic-info 2025-07-29 14:50:59.781 [main] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Start completed.
[] paas-center-traffic-info 2025-07-29 14:51:02.492 [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean -| For 'paas-center-core' URL not provided. Will try picking an instance via load-balancing.
[] paas-center-traffic-info 2025-07-29 14:51:04.196 [main] INFO  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration -| a producer (test_topic_producer_ali_sdk) init on namesrv **************:9876
[] paas-center-traffic-info 2025-07-29 14:51:05.434 [main] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingProducer -| 》》》》  Initialized loading RocketMQ producer Success 》》》》
[] paas-center-traffic-info 2025-07-29 14:51:07.088 [main] INFO  org.springframework.cloud.commons.util.InetUtils -| Cannot determine local hostname
[] paas-center-traffic-info 2025-07-29 14:51:09.312 [main] INFO  org.springframework.cloud.commons.util.InetUtils -| Cannot determine local hostname
[] paas-center-traffic-info 2025-07-29 14:51:09.439 [main] WARN  org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger -| Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[] paas-center-traffic-info 2025-07-29 14:51:09.450 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver -| Exposing 2 endpoint(s) beneath base path '/actuator'
[] paas-center-traffic-info 2025-07-29 14:51:09.541 [main] INFO  org.apache.coyote.http11.Http11NioProtocol -| Starting ProtocolHandler ["http-nio-18190"]
[] paas-center-traffic-info 2025-07-29 14:51:09.564 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer -| Tomcat started on port(s): 18190 (http) with context path ''
[] paas-center-traffic-info 2025-07-29 14:51:09.608 [main] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[] paas-center-traffic-info 2025-07-29 14:51:09.608 [main] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[] paas-center-traffic-info 2025-07-29 14:51:09.761 [main] INFO  com.alibaba.cloud.nacos.registry.NacosServiceRegistry -| nacos registry, armcloud-paas-docker paas-center-traffic-info *************:18190 register finished
[] paas-center-traffic-info 2025-07-29 14:51:10.924 [main] INFO  org.springframework.cloud.commons.util.InetUtils -| Cannot determine local hostname
[] paas-center-traffic-info 2025-07-29 14:51:10.945 [main] INFO  net.armcloud.paascenter.traffic.info.service.impl.DiskInfoServiceImpl -| 初始化ClickHouse磁盘信息缓冲区, batchSize=1000, flushInterval=2s, capacity=10000
[] paas-center-traffic-info 2025-07-29 14:51:11.017 [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean -| For 'paas-center-core' URL not provided. Will try picking an instance via load-balancing.
[] paas-center-traffic-info 2025-07-29 14:51:11.027 [main] INFO  net.armcloud.paascenter.traffic.info.service.impl.PadTrafficInfoServiceImpl -| 初始化磁盘信息缓冲区, batchSize=10, flushInterval=5s, capacity=1000
[] paas-center-traffic-info 2025-07-29 14:51:11.054 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| Started PaasCenterTrafficInfoApplication in 19.793 seconds (JVM running for 20.667)
[] paas-center-traffic-info 2025-07-29 14:51:11.068 [main] DEBUG com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner -|   ...  DDL start create  ...  
[] paas-center-traffic-info 2025-07-29 14:51:11.070 [main] DEBUG com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner -|   ...  DDL end create  ...  
[] paas-center-traffic-info 2025-07-29 14:51:11.070 [main] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initializing rocketmq consumer...
[] paas-center-traffic-info 2025-07-29 14:51:11.072 [rocketMqConsumer-1-t-3] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic pad_system_config_data 
[] paas-center-traffic-info 2025-07-29 14:51:11.072 [rocketMqConsumer-1-t-4] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic pad_traffic_data 
[] paas-center-traffic-info 2025-07-29 14:51:11.072 [rocketMqConsumer-1-t-5] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic pad_traffic_info_data 
[] paas-center-traffic-info 2025-07-29 14:51:11.072 [rocketMqConsumer-1-t-1] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic cbs_traffic_disk_data 
[] paas-center-traffic-info 2025-07-29 14:51:11.072 [rocketMqConsumer-1-t-2] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic device_system_config_data 
[] paas-center-traffic-info 2025-07-29 14:51:11.165 [main] INFO  com.alibaba.cloud.nacos.refresh.NacosContextRefresher -| [Nacos Config] Listening config: dataId=paas-center-traffic-info-docker.yaml, group=armcloud-paas-docker
[] paas-center-traffic-info 2025-07-29 14:51:11.356 [RMI TCP Connection(3)-************] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-2 - Starting...
[] paas-center-traffic-info 2025-07-29 14:51:11.559 [RMI TCP Connection(6)-************] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
[] paas-center-traffic-info 2025-07-29 14:51:11.559 [RMI TCP Connection(6)-************] INFO  org.springframework.web.servlet.DispatcherServlet -| Initializing Servlet 'dispatcherServlet'
[] paas-center-traffic-info 2025-07-29 14:51:11.582 [RMI TCP Connection(6)-************] INFO  org.springframework.web.servlet.DispatcherServlet -| Completed initialization in 23 ms
[] paas-center-traffic-info 2025-07-29 14:51:14.764 [RMI TCP Connection(3)-************] ERROR com.zaxxer.hikari.pool.HikariPool -| HikariPool-2 - Exception during pool initialization.
java.sql.SQLException: Connection refused: connect, server ClickHouseNode [uri=http://localhost:8123/armcloud, options={distributed_connections_pool_size=20,compress=1,max_parallel_replicas=2,keep_alive_timeout=30000,data_transfer_timeout=120000,max_partitions_per_insert_block=100,load_balancing=roundrobin,socket_timeout=60000,connection_timeout=30000}]@154909499
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:85)
	at com.clickhouse.jdbc.SqlExceptionUtils.create(SqlExceptionUtils.java:31)
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:90)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:131)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:335)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:288)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:157)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:41)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:330)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.getProduct(DataSourceHealthIndicator.java:122)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doDataSourceHealthCheck(DataSourceHealthIndicator.java:105)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doHealthCheck(DataSourceHealthIndicator.java:100)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:122)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468)
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76)
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309)
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401)
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at sun.reflect.GeneratedMethodAccessor98.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357)
	at sun.rmi.transport.Transport$1.run(Transport.java:200)
	at sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1340)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1315)
	at com.clickhouse.client.http.HttpUrlConnectionImpl.post(HttpUrlConnectionImpl.java:225)
	at com.clickhouse.client.http.ClickHouseHttpClient.send(ClickHouseHttpClient.java:124)
	at com.clickhouse.client.AbstractClient.execute(AbstractClient.java:280)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.sendOnce(ClickHouseClientBuilder.java:282)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.send(ClickHouseClientBuilder.java:294)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.execute(ClickHouseClientBuilder.java:349)
	at com.clickhouse.client.ClickHouseClient.executeAndWait(ClickHouseClient.java:1056)
	at com.clickhouse.client.ClickHouseRequest.executeAndWait(ClickHouseRequest.java:2154)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:128)
	... 64 common frames omitted
[] paas-center-traffic-info 2025-07-29 14:51:14.768 [RMI TCP Connection(3)-************] WARN  org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator -| DataSource health check failed
org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException: Connection refused: connect, server ClickHouseNode [uri=http://localhost:8123/armcloud, options={distributed_connections_pool_size=20,compress=1,max_parallel_replicas=2,keep_alive_timeout=30000,data_transfer_timeout=120000,max_partitions_per_insert_block=100,load_balancing=roundrobin,socket_timeout=60000,connection_timeout=30000}]@154909499
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:83)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:330)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.getProduct(DataSourceHealthIndicator.java:122)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doDataSourceHealthCheck(DataSourceHealthIndicator.java:105)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doHealthCheck(DataSourceHealthIndicator.java:100)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:122)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468)
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76)
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309)
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401)
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at sun.reflect.GeneratedMethodAccessor98.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357)
	at sun.rmi.transport.Transport$1.run(Transport.java:200)
	at sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLException: Connection refused: connect, server ClickHouseNode [uri=http://localhost:8123/armcloud, options={distributed_connections_pool_size=20,compress=1,max_parallel_replicas=2,keep_alive_timeout=30000,data_transfer_timeout=120000,max_partitions_per_insert_block=100,load_balancing=roundrobin,socket_timeout=60000,connection_timeout=30000}]@154909499
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:85)
	at com.clickhouse.jdbc.SqlExceptionUtils.create(SqlExceptionUtils.java:31)
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:90)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:131)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:335)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:288)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:157)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:41)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	... 50 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1340)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1315)
	at com.clickhouse.client.http.HttpUrlConnectionImpl.post(HttpUrlConnectionImpl.java:225)
	at com.clickhouse.client.http.ClickHouseHttpClient.send(ClickHouseHttpClient.java:124)
	at com.clickhouse.client.AbstractClient.execute(AbstractClient.java:280)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.sendOnce(ClickHouseClientBuilder.java:282)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.send(ClickHouseClientBuilder.java:294)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.execute(ClickHouseClientBuilder.java:349)
	at com.clickhouse.client.ClickHouseClient.executeAndWait(ClickHouseClient.java:1056)
	at com.clickhouse.client.ClickHouseRequest.executeAndWait(ClickHouseRequest.java:2154)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:128)
	... 64 common frames omitted
[] paas-center-traffic-info 2025-07-29 14:51:14.768 [RMI TCP Connection(3)-************] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Starting...
[] paas-center-traffic-info 2025-07-29 14:51:14.775 [RMI TCP Connection(3)-************] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Start completed.
[] paas-center-traffic-info 2025-07-29 14:51:50.497 [http-nio-18190-exec-2] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| 查询Ceph压力折线图数据: CephPressureQueryDTO(clusterCode=null, startTime=null, endTime=null)
[] paas-center-traffic-info 2025-07-29 14:51:50.509 [http-nio-18190-exec-2] WARN  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 查询参数不完整: CephPressureQueryDTO(clusterCode=null, startTime=null, endTime=null)
[] paas-center-traffic-info 2025-07-29 14:51:50.510 [http-nio-18190-exec-2] ERROR net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| 查询Ceph压力折线图数据异常
java.lang.IllegalArgumentException: 查询参数不完整
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl.getChartData(CephPressureDataServiceImpl.java:85)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl$$FastClassBySpringCGLIB$$c3d291d4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl$$EnhancerBySpringCGLIB$$55ac6da8.getChartData(<generated>)
	at net.armcloud.paascenter.traffic.info.controller.TrafficDataController.getCephPressureChart(TrafficDataController.java:118)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.armcloud.paascenter.traffic.info.filter.RequestBodyCacheFilter.doFilter(RequestBodyCacheFilter.java:26)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
[] paas-center-traffic-info 2025-07-29 14:52:17.511 [Thread-4] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder -| [HttpClientBeanHolder] Start destroying common HttpClient
[] paas-center-traffic-info 2025-07-29 14:52:17.511 [Thread-10] WARN  com.alibaba.nacos.common.notify.NotifyCenter -| [NotifyCenter] Start destroying Publisher
[] paas-center-traffic-info 2025-07-29 14:52:17.512 [Thread-10] WARN  com.alibaba.nacos.common.notify.NotifyCenter -| [NotifyCenter] Destruction of the end
[] paas-center-traffic-info 2025-07-29 14:52:17.512 [Thread-4] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder -| [HttpClientBeanHolder] Destruction of the end
[] paas-center-traffic-info 2025-07-29 14:52:17.663 [SpringApplicationShutdownHook] INFO  com.alibaba.cloud.nacos.registry.NacosServiceRegistry -| De-registering from Nacos Server now...
[] paas-center-traffic-info 2025-07-29 14:52:17.668 [SpringApplicationShutdownHook] INFO  com.alibaba.cloud.nacos.registry.NacosServiceRegistry -| De-registration finished.
[] paas-center-traffic-info 2025-07-29 14:52:17.712 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoSendMsgService -| 关闭缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 14:52:20.804 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Shutdown initiated...
[] paas-center-traffic-info 2025-07-29 14:52:20.818 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Shutdown completed.
[] paas-center-traffic-info 2025-07-29 14:52:20.819 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoClickHouseService -| 关闭ClickHouse磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 14:52:20.819 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Shutdown initiated...
[] paas-center-traffic-info 2025-07-29 14:52:20.826 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Shutdown completed.
[] paas-center-traffic-info 2025-07-29 14:52:20.827 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.impl.DiskInfoServiceImpl -| 关闭ClickHouse磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 14:52:20.827 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.impl.PadTrafficInfoServiceImpl -| 关闭磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 14:52:28.454 [background-preinit] INFO  org.hibernate.validator.internal.util.Version -| HV000001: Hibernate Validator 6.2.5.Final
[] paas-center-traffic-info 2025-07-29 14:52:28.488 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| Starting PaasCenterTrafficInfoApplication using Java 1.8.0_252 on DESKTOP-D51FIJ4 with PID 46068 (D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes started by xskj in D:\dev\workspace\paas-center-traffic-info)
[] paas-center-traffic-info 2025-07-29 14:52:28.489 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| The following 1 profile is active: "docker"
[] paas-center-traffic-info 2025-07-29 14:52:28.548 [main] INFO  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader -| [Nacos Config] Load config[dataId=paas-center-traffic-info-docker.yaml, group=armcloud-paas-docker] success
[] paas-center-traffic-info 2025-07-29 14:52:29.534 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Multiple Spring Data modules found, entering strict repository configuration mode
[] paas-center-traffic-info 2025-07-29 14:52:29.538 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[] paas-center-traffic-info 2025-07-29 14:52:29.565 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
[] paas-center-traffic-info 2025-07-29 14:52:29.906 [main] INFO  org.springframework.cloud.context.scope.GenericScope -| BeanFactory id=96fb6da4-8676-337b-a389-f874aef18e94
[] paas-center-traffic-info 2025-07-29 14:52:30.183 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$EnhancerBySpringCGLIB$$4c34bc8c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:52:30.187 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$EnhancerBySpringCGLIB$$f4fac5f0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:52:30.306 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:52:30.314 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:52:30.316 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:52:30.316 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$537/52562984] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:52:30.317 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:52:30.324 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:52:30.328 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:52:30.656 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer -| Tomcat initialized with port(s): 18190 (http)
[] paas-center-traffic-info 2025-07-29 14:52:30.670 [main] INFO  org.apache.coyote.http11.Http11NioProtocol -| Initializing ProtocolHandler ["http-nio-18190"]
[] paas-center-traffic-info 2025-07-29 14:52:30.671 [main] INFO  org.apache.catalina.core.StandardService -| Starting service [Tomcat]
[] paas-center-traffic-info 2025-07-29 14:52:30.671 [main] INFO  org.apache.catalina.core.StandardEngine -| Starting Servlet engine: [Apache Tomcat/9.0.68]
[] paas-center-traffic-info 2025-07-29 14:52:30.821 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] -| Initializing Spring embedded WebApplicationContext
[] paas-center-traffic-info 2025-07-29 14:52:30.821 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext -| Root WebApplicationContext: initialization completed in 2258 ms
[] paas-center-traffic-info 2025-07-29 14:52:31.129 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 初始化ClickHouse数据源...
[] paas-center-traffic-info 2025-07-29 14:52:31.129 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 成功加载 ClickHouse 驱动类
[] paas-center-traffic-info 2025-07-29 14:52:31.129 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| ClickHouse DataSource bean created successfully
[] paas-center-traffic-info 2025-07-29 14:52:31.134 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 创建ClickHouse SQLSessionFactory, 数据源类型: com.zaxxer.hikari.HikariDataSource
[] paas-center-traffic-info 2025-07-29 14:52:31.336 [main] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoClickHouseService -| 初始化ClickHouse磁盘信息缓冲区, batchSize=100, flushInterval=10s, capacity=10000
[] paas-center-traffic-info 2025-07-29 14:52:31.375 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Property 'configuration' or 'configLocation' not specified, using default MyBatis Configuration
[] paas-center-traffic-info 2025-07-29 14:52:31.527 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\CephPressureDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:52:31.546 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\NetStoragePadUnitDetailMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:52:31.662 [main] DEBUG com.baomidou.mybatisplus.core.toolkit.Sequence -| Initialization Sequence datacenterId:0 workerId:0
[] paas-center-traffic-info 2025-07-29 14:52:32.106 [main] INFO  org.redisson.Version -| Redisson 3.17.2
[] paas-center-traffic-info 2025-07-29 14:52:32.867 [redisson-netty-2-12] INFO  org.redisson.connection.pool.MasterPubSubConnectionPool -| 1 connections initialized for **************/**************:6379
[] paas-center-traffic-info 2025-07-29 14:52:32.905 [redisson-netty-2-18] INFO  org.redisson.connection.pool.MasterConnectionPool -| 24 connections initialized for **************/**************:6379
[] paas-center-traffic-info 2025-07-29 14:52:33.194 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Property 'configuration' or 'configLocation' not specified, using default MyBatis Configuration
[] paas-center-traffic-info 2025-07-29 14:52:33.202 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\traffic\DeviceSystemConfigDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:52:33.206 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\traffic\PadSystemConfigDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:52:33.209 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\traffic\PadTrafficInfoMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:52:33.329 [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean -| For 'paas-center-core' URL not provided. Will try picking an instance via load-balancing.
[] paas-center-traffic-info 2025-07-29 14:52:33.511 [main] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Starting...
[] paas-center-traffic-info 2025-07-29 14:52:33.835 [main] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Start completed.
[] paas-center-traffic-info 2025-07-29 14:52:34.082 [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean -| For 'paas-center-core' URL not provided. Will try picking an instance via load-balancing.
[] paas-center-traffic-info 2025-07-29 14:52:35.070 [main] INFO  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration -| a producer (test_topic_producer_ali_sdk) init on namesrv **************:9876
[] paas-center-traffic-info 2025-07-29 14:52:36.376 [main] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingProducer -| 》》》》  Initialized loading RocketMQ producer Success 》》》》
[] paas-center-traffic-info 2025-07-29 14:52:38.061 [main] INFO  org.springframework.cloud.commons.util.InetUtils -| Cannot determine local hostname
[] paas-center-traffic-info 2025-07-29 14:52:40.329 [main] INFO  org.springframework.cloud.commons.util.InetUtils -| Cannot determine local hostname
[] paas-center-traffic-info 2025-07-29 14:52:40.469 [main] WARN  org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger -| Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[] paas-center-traffic-info 2025-07-29 14:52:40.479 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver -| Exposing 2 endpoint(s) beneath base path '/actuator'
[] paas-center-traffic-info 2025-07-29 14:52:40.571 [main] INFO  org.apache.coyote.http11.Http11NioProtocol -| Starting ProtocolHandler ["http-nio-18190"]
[] paas-center-traffic-info 2025-07-29 14:52:40.595 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer -| Tomcat started on port(s): 18190 (http) with context path ''
[] paas-center-traffic-info 2025-07-29 14:52:40.637 [main] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[] paas-center-traffic-info 2025-07-29 14:52:40.637 [main] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[] paas-center-traffic-info 2025-07-29 14:52:40.789 [main] INFO  com.alibaba.cloud.nacos.registry.NacosServiceRegistry -| nacos registry, armcloud-paas-docker paas-center-traffic-info *************:18190 register finished
[] paas-center-traffic-info 2025-07-29 14:52:41.951 [main] INFO  org.springframework.cloud.commons.util.InetUtils -| Cannot determine local hostname
[] paas-center-traffic-info 2025-07-29 14:52:41.966 [main] INFO  net.armcloud.paascenter.traffic.info.service.impl.DiskInfoServiceImpl -| 初始化ClickHouse磁盘信息缓冲区, batchSize=1000, flushInterval=2s, capacity=10000
[] paas-center-traffic-info 2025-07-29 14:52:42.040 [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean -| For 'paas-center-core' URL not provided. Will try picking an instance via load-balancing.
[] paas-center-traffic-info 2025-07-29 14:52:42.050 [main] INFO  net.armcloud.paascenter.traffic.info.service.impl.PadTrafficInfoServiceImpl -| 初始化磁盘信息缓冲区, batchSize=10, flushInterval=5s, capacity=1000
[] paas-center-traffic-info 2025-07-29 14:52:42.076 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| Started PaasCenterTrafficInfoApplication in 16.884 seconds (JVM running for 17.863)
[] paas-center-traffic-info 2025-07-29 14:52:42.091 [main] DEBUG com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner -|   ...  DDL start create  ...  
[] paas-center-traffic-info 2025-07-29 14:52:42.092 [main] DEBUG com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner -|   ...  DDL end create  ...  
[] paas-center-traffic-info 2025-07-29 14:52:42.093 [main] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initializing rocketmq consumer...
[] paas-center-traffic-info 2025-07-29 14:52:42.094 [rocketMqConsumer-1-t-3] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic pad_system_config_data 
[] paas-center-traffic-info 2025-07-29 14:52:42.094 [rocketMqConsumer-1-t-2] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic device_system_config_data 
[] paas-center-traffic-info 2025-07-29 14:52:42.094 [rocketMqConsumer-1-t-1] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic cbs_traffic_disk_data 
[] paas-center-traffic-info 2025-07-29 14:52:42.094 [rocketMqConsumer-1-t-5] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic pad_traffic_info_data 
[] paas-center-traffic-info 2025-07-29 14:52:42.094 [rocketMqConsumer-1-t-4] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic pad_traffic_data 
[] paas-center-traffic-info 2025-07-29 14:52:42.200 [main] INFO  com.alibaba.cloud.nacos.refresh.NacosContextRefresher -| [Nacos Config] Listening config: dataId=paas-center-traffic-info-docker.yaml, group=armcloud-paas-docker
[] paas-center-traffic-info 2025-07-29 14:52:42.594 [RMI TCP Connection(3)-************] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
[] paas-center-traffic-info 2025-07-29 14:52:42.594 [RMI TCP Connection(3)-************] INFO  org.springframework.web.servlet.DispatcherServlet -| Initializing Servlet 'dispatcherServlet'
[] paas-center-traffic-info 2025-07-29 14:52:42.598 [RMI TCP Connection(3)-************] INFO  org.springframework.web.servlet.DispatcherServlet -| Completed initialization in 4 ms
[] paas-center-traffic-info 2025-07-29 14:52:42.780 [RMI TCP Connection(5)-************] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-2 - Starting...
[] paas-center-traffic-info 2025-07-29 14:52:46.155 [RMI TCP Connection(5)-************] ERROR com.zaxxer.hikari.pool.HikariPool -| HikariPool-2 - Exception during pool initialization.
java.sql.SQLException: Connection refused: connect, server ClickHouseNode [uri=http://localhost:8123/armcloud, options={distributed_connections_pool_size=20,compress=1,max_parallel_replicas=2,keep_alive_timeout=30000,data_transfer_timeout=120000,max_partitions_per_insert_block=100,load_balancing=roundrobin,socket_timeout=60000,connection_timeout=30000}]@**********
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:85)
	at com.clickhouse.jdbc.SqlExceptionUtils.create(SqlExceptionUtils.java:31)
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:90)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:131)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:335)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:288)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:157)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:41)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:330)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.getProduct(DataSourceHealthIndicator.java:122)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doDataSourceHealthCheck(DataSourceHealthIndicator.java:105)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doHealthCheck(DataSourceHealthIndicator.java:100)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:122)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468)
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76)
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309)
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401)
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at sun.reflect.GeneratedMethodAccessor98.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357)
	at sun.rmi.transport.Transport$1.run(Transport.java:200)
	at sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1340)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1315)
	at com.clickhouse.client.http.HttpUrlConnectionImpl.post(HttpUrlConnectionImpl.java:225)
	at com.clickhouse.client.http.ClickHouseHttpClient.send(ClickHouseHttpClient.java:124)
	at com.clickhouse.client.AbstractClient.execute(AbstractClient.java:280)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.sendOnce(ClickHouseClientBuilder.java:282)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.send(ClickHouseClientBuilder.java:294)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.execute(ClickHouseClientBuilder.java:349)
	at com.clickhouse.client.ClickHouseClient.executeAndWait(ClickHouseClient.java:1056)
	at com.clickhouse.client.ClickHouseRequest.executeAndWait(ClickHouseRequest.java:2154)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:128)
	... 64 common frames omitted
[] paas-center-traffic-info 2025-07-29 14:52:46.161 [RMI TCP Connection(5)-************] WARN  org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator -| DataSource health check failed
org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException: Connection refused: connect, server ClickHouseNode [uri=http://localhost:8123/armcloud, options={distributed_connections_pool_size=20,compress=1,max_parallel_replicas=2,keep_alive_timeout=30000,data_transfer_timeout=120000,max_partitions_per_insert_block=100,load_balancing=roundrobin,socket_timeout=60000,connection_timeout=30000}]@**********
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:83)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:330)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.getProduct(DataSourceHealthIndicator.java:122)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doDataSourceHealthCheck(DataSourceHealthIndicator.java:105)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doHealthCheck(DataSourceHealthIndicator.java:100)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:122)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468)
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76)
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309)
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401)
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at sun.reflect.GeneratedMethodAccessor98.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357)
	at sun.rmi.transport.Transport$1.run(Transport.java:200)
	at sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLException: Connection refused: connect, server ClickHouseNode [uri=http://localhost:8123/armcloud, options={distributed_connections_pool_size=20,compress=1,max_parallel_replicas=2,keep_alive_timeout=30000,data_transfer_timeout=120000,max_partitions_per_insert_block=100,load_balancing=roundrobin,socket_timeout=60000,connection_timeout=30000}]@**********
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:85)
	at com.clickhouse.jdbc.SqlExceptionUtils.create(SqlExceptionUtils.java:31)
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:90)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:131)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:335)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:288)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:157)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:41)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	... 50 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1340)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1315)
	at com.clickhouse.client.http.HttpUrlConnectionImpl.post(HttpUrlConnectionImpl.java:225)
	at com.clickhouse.client.http.ClickHouseHttpClient.send(ClickHouseHttpClient.java:124)
	at com.clickhouse.client.AbstractClient.execute(AbstractClient.java:280)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.sendOnce(ClickHouseClientBuilder.java:282)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.send(ClickHouseClientBuilder.java:294)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.execute(ClickHouseClientBuilder.java:349)
	at com.clickhouse.client.ClickHouseClient.executeAndWait(ClickHouseClient.java:1056)
	at com.clickhouse.client.ClickHouseRequest.executeAndWait(ClickHouseRequest.java:2154)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:128)
	... 64 common frames omitted
[] paas-center-traffic-info 2025-07-29 14:52:46.161 [RMI TCP Connection(5)-************] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Starting...
[] paas-center-traffic-info 2025-07-29 14:52:46.168 [RMI TCP Connection(5)-************] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Start completed.
[] paas-center-traffic-info 2025-07-29 14:52:50.819 [http-nio-18190-exec-1] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| 查询Ceph压力折线图数据: CephPressureQueryDTO(clusterCode=null, startTime=null, endTime=null)
[] paas-center-traffic-info 2025-07-29 14:52:50.819 [http-nio-18190-exec-1] WARN  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| 查询参数不完整: CephPressureQueryDTO(clusterCode=null, startTime=null, endTime=null)
[] paas-center-traffic-info 2025-07-29 14:53:14.502 [http-nio-18190-exec-3] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| 查询Ceph压力折线图数据: CephPressureQueryDTO(clusterCode=null, startTime=Tue Jul 29 14:30:00 CST 2025, endTime=null)
[] paas-center-traffic-info 2025-07-29 14:53:14.502 [http-nio-18190-exec-3] WARN  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| 查询参数不完整: CephPressureQueryDTO(clusterCode=null, startTime=Tue Jul 29 14:30:00 CST 2025, endTime=null)
[] paas-center-traffic-info 2025-07-29 14:53:28.234 [http-nio-18190-exec-4] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| 查询Ceph压力折线图数据: CephPressureQueryDTO(clusterCode=null, startTime=Tue Jul 29 14:30:00 CST 2025, endTime=Tue Jul 29 15:30:00 CST 2025)
[] paas-center-traffic-info 2025-07-29 14:53:28.258 [http-nio-18190-exec-4] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 从分钟聚合表查询数据: clusterCode=null, dataCount=0
[] paas-center-traffic-info 2025-07-29 14:53:28.495 [http-nio-18190-exec-4] ERROR net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 查询Ceph压力折线图数据失败
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'maxValue,
            min_value as minValue,
            data_count as dataCount' at line 6
### The error may exist in file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\CephPressureDataMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT             cluster_code as clusterCode,             DATE_FORMAT(minute_time, '%Y-%m-%d %H:%i:00') as timeStr,             minute_time as timePoint,             avg_value as avgValue,             max_value as maxValue,             min_value as minValue,             data_count as dataCount,             metric_unit as metricUnit         FROM cluster_monitor_minute_data         WHERE 1=1                   AND metric_type = ?         AND minute_time BETWEEN ? AND ?         ORDER BY cluster_code, minute_time ASC
### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'maxValue,
            min_value as minValue,
            data_count as dataCount' at line 6
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'maxValue,
            min_value as minValue,
            data_count as dataCount' at line 6
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy115.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy123.selectMinuteDataFromAgg(Unknown Source)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl.queryFromMinuteTable(CephPressureDataServiceImpl.java:135)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl.getChartData(CephPressureDataServiceImpl.java:94)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl$$FastClassBySpringCGLIB$$c3d291d4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl$$EnhancerBySpringCGLIB$$59724d66.getChartData(<generated>)
	at net.armcloud.paascenter.traffic.info.controller.TrafficDataController.getCephPressureChart(TrafficDataController.java:123)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.armcloud.paascenter.traffic.info.filter.RequestBodyCacheFilter.doFilter(RequestBodyCacheFilter.java:26)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'maxValue,
            min_value as minValue,
            data_count as dataCount' at line 6
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:89)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 73 common frames omitted
[] paas-center-traffic-info 2025-07-29 14:53:28.495 [http-nio-18190-exec-4] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| 查询到0条Ceph压力数据
[] paas-center-traffic-info 2025-07-29 14:58:52.476 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder -| [HttpClientBeanHolder] Start destroying common HttpClient
[] paas-center-traffic-info 2025-07-29 14:58:52.477 [Thread-10] WARN  com.alibaba.nacos.common.notify.NotifyCenter -| [NotifyCenter] Start destroying Publisher
[] paas-center-traffic-info 2025-07-29 14:58:52.477 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder -| [HttpClientBeanHolder] Destruction of the end
[] paas-center-traffic-info 2025-07-29 14:58:52.477 [Thread-10] WARN  com.alibaba.nacos.common.notify.NotifyCenter -| [NotifyCenter] Destruction of the end
[] paas-center-traffic-info 2025-07-29 14:58:52.629 [SpringApplicationShutdownHook] INFO  com.alibaba.cloud.nacos.registry.NacosServiceRegistry -| De-registering from Nacos Server now...
[] paas-center-traffic-info 2025-07-29 14:58:52.632 [SpringApplicationShutdownHook] INFO  com.alibaba.cloud.nacos.registry.NacosServiceRegistry -| De-registration finished.
[] paas-center-traffic-info 2025-07-29 14:58:52.674 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoSendMsgService -| 关闭缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 14:58:55.808 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Shutdown initiated...
[] paas-center-traffic-info 2025-07-29 14:58:55.822 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Shutdown completed.
[] paas-center-traffic-info 2025-07-29 14:58:55.822 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoClickHouseService -| 关闭ClickHouse磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 14:58:55.822 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Shutdown initiated...
[] paas-center-traffic-info 2025-07-29 14:58:55.829 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Shutdown completed.
[] paas-center-traffic-info 2025-07-29 14:58:55.830 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.impl.DiskInfoServiceImpl -| 关闭ClickHouse磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 14:58:55.830 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.impl.PadTrafficInfoServiceImpl -| 关闭磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 14:59:03.606 [background-preinit] INFO  org.hibernate.validator.internal.util.Version -| HV000001: Hibernate Validator 6.2.5.Final
[] paas-center-traffic-info 2025-07-29 14:59:03.630 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| Starting PaasCenterTrafficInfoApplication using Java 1.8.0_252 on DESKTOP-D51FIJ4 with PID 8100 (D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes started by xskj in D:\dev\workspace\paas-center-traffic-info)
[] paas-center-traffic-info 2025-07-29 14:59:03.631 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| The following 1 profile is active: "docker"
[] paas-center-traffic-info 2025-07-29 14:59:03.688 [main] INFO  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader -| [Nacos Config] Load config[dataId=paas-center-traffic-info-docker.yaml, group=armcloud-paas-docker] success
[] paas-center-traffic-info 2025-07-29 14:59:04.704 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Multiple Spring Data modules found, entering strict repository configuration mode
[] paas-center-traffic-info 2025-07-29 14:59:04.707 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[] paas-center-traffic-info 2025-07-29 14:59:04.732 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
[] paas-center-traffic-info 2025-07-29 14:59:05.046 [main] INFO  org.springframework.cloud.context.scope.GenericScope -| BeanFactory id=96fb6da4-8676-337b-a389-f874aef18e94
[] paas-center-traffic-info 2025-07-29 14:59:05.311 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$EnhancerBySpringCGLIB$$6759c900] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:59:05.315 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$EnhancerBySpringCGLIB$$101fd264] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:59:05.434 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:59:05.441 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:59:05.442 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:59:05.442 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$537/116112390] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:59:05.444 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:59:05.453 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:59:05.457 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 14:59:05.786 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer -| Tomcat initialized with port(s): 18190 (http)
[] paas-center-traffic-info 2025-07-29 14:59:05.799 [main] INFO  org.apache.coyote.http11.Http11NioProtocol -| Initializing ProtocolHandler ["http-nio-18190"]
[] paas-center-traffic-info 2025-07-29 14:59:05.800 [main] INFO  org.apache.catalina.core.StandardService -| Starting service [Tomcat]
[] paas-center-traffic-info 2025-07-29 14:59:05.800 [main] INFO  org.apache.catalina.core.StandardEngine -| Starting Servlet engine: [Apache Tomcat/9.0.68]
[] paas-center-traffic-info 2025-07-29 14:59:05.941 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] -| Initializing Spring embedded WebApplicationContext
[] paas-center-traffic-info 2025-07-29 14:59:05.941 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext -| Root WebApplicationContext: initialization completed in 2243 ms
[] paas-center-traffic-info 2025-07-29 14:59:06.240 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 初始化ClickHouse数据源...
[] paas-center-traffic-info 2025-07-29 14:59:06.240 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 成功加载 ClickHouse 驱动类
[] paas-center-traffic-info 2025-07-29 14:59:06.240 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| ClickHouse DataSource bean created successfully
[] paas-center-traffic-info 2025-07-29 14:59:06.246 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 创建ClickHouse SQLSessionFactory, 数据源类型: com.zaxxer.hikari.HikariDataSource
[] paas-center-traffic-info 2025-07-29 14:59:06.475 [main] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoClickHouseService -| 初始化ClickHouse磁盘信息缓冲区, batchSize=100, flushInterval=10s, capacity=10000
[] paas-center-traffic-info 2025-07-29 14:59:06.501 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Property 'configuration' or 'configLocation' not specified, using default MyBatis Configuration
[] paas-center-traffic-info 2025-07-29 14:59:06.634 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\CephPressureDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:59:06.652 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\NetStoragePadUnitDetailMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:59:06.765 [main] DEBUG com.baomidou.mybatisplus.core.toolkit.Sequence -| Initialization Sequence datacenterId:0 workerId:9
[] paas-center-traffic-info 2025-07-29 14:59:07.190 [main] INFO  org.redisson.Version -| Redisson 3.17.2
[] paas-center-traffic-info 2025-07-29 14:59:07.952 [redisson-netty-2-8] INFO  org.redisson.connection.pool.MasterPubSubConnectionPool -| 1 connections initialized for **************/**************:6379
[] paas-center-traffic-info 2025-07-29 14:59:07.983 [redisson-netty-2-19] INFO  org.redisson.connection.pool.MasterConnectionPool -| 24 connections initialized for **************/**************:6379
[] paas-center-traffic-info 2025-07-29 14:59:08.280 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Property 'configuration' or 'configLocation' not specified, using default MyBatis Configuration
[] paas-center-traffic-info 2025-07-29 14:59:08.302 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\traffic\DeviceSystemConfigDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:59:08.305 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\traffic\PadSystemConfigDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:59:08.308 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\traffic\PadTrafficInfoMapper.xml]'
[] paas-center-traffic-info 2025-07-29 14:59:08.435 [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean -| For 'paas-center-core' URL not provided. Will try picking an instance via load-balancing.
[] paas-center-traffic-info 2025-07-29 14:59:08.650 [main] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Starting...
[] paas-center-traffic-info 2025-07-29 14:59:09.004 [main] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Start completed.
[] paas-center-traffic-info 2025-07-29 14:59:09.240 [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean -| For 'paas-center-core' URL not provided. Will try picking an instance via load-balancing.
[] paas-center-traffic-info 2025-07-29 14:59:10.143 [main] INFO  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration -| a producer (test_topic_producer_ali_sdk) init on namesrv **************:9876
[] paas-center-traffic-info 2025-07-29 14:59:11.423 [main] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingProducer -| 》》》》  Initialized loading RocketMQ producer Success 》》》》
[] paas-center-traffic-info 2025-07-29 14:59:13.122 [main] INFO  org.springframework.cloud.commons.util.InetUtils -| Cannot determine local hostname
[] paas-center-traffic-info 2025-07-29 14:59:15.344 [main] INFO  org.springframework.cloud.commons.util.InetUtils -| Cannot determine local hostname
[] paas-center-traffic-info 2025-07-29 14:59:15.476 [main] WARN  org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger -| Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[] paas-center-traffic-info 2025-07-29 14:59:15.486 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver -| Exposing 2 endpoint(s) beneath base path '/actuator'
[] paas-center-traffic-info 2025-07-29 14:59:15.579 [main] INFO  org.apache.coyote.http11.Http11NioProtocol -| Starting ProtocolHandler ["http-nio-18190"]
[] paas-center-traffic-info 2025-07-29 14:59:15.602 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer -| Tomcat started on port(s): 18190 (http) with context path ''
[] paas-center-traffic-info 2025-07-29 14:59:15.645 [main] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[] paas-center-traffic-info 2025-07-29 14:59:15.645 [main] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[] paas-center-traffic-info 2025-07-29 14:59:15.795 [main] INFO  com.alibaba.cloud.nacos.registry.NacosServiceRegistry -| nacos registry, armcloud-paas-docker paas-center-traffic-info *************:18190 register finished
[] paas-center-traffic-info 2025-07-29 14:59:16.959 [main] INFO  org.springframework.cloud.commons.util.InetUtils -| Cannot determine local hostname
[] paas-center-traffic-info 2025-07-29 14:59:16.980 [main] INFO  net.armcloud.paascenter.traffic.info.service.impl.DiskInfoServiceImpl -| 初始化ClickHouse磁盘信息缓冲区, batchSize=1000, flushInterval=2s, capacity=10000
[] paas-center-traffic-info 2025-07-29 14:59:17.057 [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean -| For 'paas-center-core' URL not provided. Will try picking an instance via load-balancing.
[] paas-center-traffic-info 2025-07-29 14:59:17.069 [main] INFO  net.armcloud.paascenter.traffic.info.service.impl.PadTrafficInfoServiceImpl -| 初始化磁盘信息缓冲区, batchSize=10, flushInterval=5s, capacity=1000
[] paas-center-traffic-info 2025-07-29 14:59:17.097 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| Started PaasCenterTrafficInfoApplication in 16.786 seconds (JVM running for 17.734)
[] paas-center-traffic-info 2025-07-29 14:59:17.112 [main] DEBUG com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner -|   ...  DDL start create  ...  
[] paas-center-traffic-info 2025-07-29 14:59:17.114 [main] DEBUG com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner -|   ...  DDL end create  ...  
[] paas-center-traffic-info 2025-07-29 14:59:17.114 [main] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initializing rocketmq consumer...
[] paas-center-traffic-info 2025-07-29 14:59:17.116 [rocketMqConsumer-1-t-2] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic device_system_config_data 
[] paas-center-traffic-info 2025-07-29 14:59:17.116 [rocketMqConsumer-1-t-4] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic pad_traffic_data 
[] paas-center-traffic-info 2025-07-29 14:59:17.116 [rocketMqConsumer-1-t-3] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic pad_system_config_data 
[] paas-center-traffic-info 2025-07-29 14:59:17.116 [rocketMqConsumer-1-t-1] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic cbs_traffic_disk_data 
[] paas-center-traffic-info 2025-07-29 14:59:17.116 [rocketMqConsumer-1-t-5] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic pad_traffic_info_data 
[] paas-center-traffic-info 2025-07-29 14:59:17.210 [main] INFO  com.alibaba.cloud.nacos.refresh.NacosContextRefresher -| [Nacos Config] Listening config: dataId=paas-center-traffic-info-docker.yaml, group=armcloud-paas-docker
[] paas-center-traffic-info 2025-07-29 14:59:17.552 [RMI TCP Connection(2)-************] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
[] paas-center-traffic-info 2025-07-29 14:59:17.552 [RMI TCP Connection(2)-************] INFO  org.springframework.web.servlet.DispatcherServlet -| Initializing Servlet 'dispatcherServlet'
[] paas-center-traffic-info 2025-07-29 14:59:17.564 [RMI TCP Connection(2)-************] INFO  org.springframework.web.servlet.DispatcherServlet -| Completed initialization in 12 ms
[] paas-center-traffic-info 2025-07-29 14:59:17.671 [RMI TCP Connection(4)-************] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-2 - Starting...
[] paas-center-traffic-info 2025-07-29 14:59:21.000 [RMI TCP Connection(4)-************] ERROR com.zaxxer.hikari.pool.HikariPool -| HikariPool-2 - Exception during pool initialization.
java.sql.SQLException: Connection refused: connect, server ClickHouseNode [uri=http://localhost:8123/armcloud, options={distributed_connections_pool_size=20,compress=1,max_parallel_replicas=2,keep_alive_timeout=30000,data_transfer_timeout=120000,max_partitions_per_insert_block=100,load_balancing=roundrobin,socket_timeout=60000,connection_timeout=30000}]@**********
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:85)
	at com.clickhouse.jdbc.SqlExceptionUtils.create(SqlExceptionUtils.java:31)
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:90)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:131)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:335)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:288)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:157)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:41)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:330)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.getProduct(DataSourceHealthIndicator.java:122)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doDataSourceHealthCheck(DataSourceHealthIndicator.java:105)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doHealthCheck(DataSourceHealthIndicator.java:100)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:122)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468)
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76)
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309)
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401)
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at sun.reflect.GeneratedMethodAccessor98.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357)
	at sun.rmi.transport.Transport$1.run(Transport.java:200)
	at sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1340)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1315)
	at com.clickhouse.client.http.HttpUrlConnectionImpl.post(HttpUrlConnectionImpl.java:225)
	at com.clickhouse.client.http.ClickHouseHttpClient.send(ClickHouseHttpClient.java:124)
	at com.clickhouse.client.AbstractClient.execute(AbstractClient.java:280)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.sendOnce(ClickHouseClientBuilder.java:282)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.send(ClickHouseClientBuilder.java:294)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.execute(ClickHouseClientBuilder.java:349)
	at com.clickhouse.client.ClickHouseClient.executeAndWait(ClickHouseClient.java:1056)
	at com.clickhouse.client.ClickHouseRequest.executeAndWait(ClickHouseRequest.java:2154)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:128)
	... 64 common frames omitted
[] paas-center-traffic-info 2025-07-29 14:59:21.006 [RMI TCP Connection(4)-************] WARN  org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator -| DataSource health check failed
org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException: Connection refused: connect, server ClickHouseNode [uri=http://localhost:8123/armcloud, options={distributed_connections_pool_size=20,compress=1,max_parallel_replicas=2,keep_alive_timeout=30000,data_transfer_timeout=120000,max_partitions_per_insert_block=100,load_balancing=roundrobin,socket_timeout=60000,connection_timeout=30000}]@**********
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:83)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:330)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.getProduct(DataSourceHealthIndicator.java:122)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doDataSourceHealthCheck(DataSourceHealthIndicator.java:105)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doHealthCheck(DataSourceHealthIndicator.java:100)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:122)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468)
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76)
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309)
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401)
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at sun.reflect.GeneratedMethodAccessor98.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357)
	at sun.rmi.transport.Transport$1.run(Transport.java:200)
	at sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLException: Connection refused: connect, server ClickHouseNode [uri=http://localhost:8123/armcloud, options={distributed_connections_pool_size=20,compress=1,max_parallel_replicas=2,keep_alive_timeout=30000,data_transfer_timeout=120000,max_partitions_per_insert_block=100,load_balancing=roundrobin,socket_timeout=60000,connection_timeout=30000}]@**********
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:85)
	at com.clickhouse.jdbc.SqlExceptionUtils.create(SqlExceptionUtils.java:31)
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:90)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:131)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:335)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:288)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:157)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:41)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	... 50 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1340)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1315)
	at com.clickhouse.client.http.HttpUrlConnectionImpl.post(HttpUrlConnectionImpl.java:225)
	at com.clickhouse.client.http.ClickHouseHttpClient.send(ClickHouseHttpClient.java:124)
	at com.clickhouse.client.AbstractClient.execute(AbstractClient.java:280)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.sendOnce(ClickHouseClientBuilder.java:282)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.send(ClickHouseClientBuilder.java:294)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.execute(ClickHouseClientBuilder.java:349)
	at com.clickhouse.client.ClickHouseClient.executeAndWait(ClickHouseClient.java:1056)
	at com.clickhouse.client.ClickHouseRequest.executeAndWait(ClickHouseRequest.java:2154)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:128)
	... 64 common frames omitted
[] paas-center-traffic-info 2025-07-29 14:59:21.006 [RMI TCP Connection(4)-************] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Starting...
[] paas-center-traffic-info 2025-07-29 14:59:21.013 [RMI TCP Connection(4)-************] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Start completed.
[] paas-center-traffic-info 2025-07-29 14:59:41.171 [http-nio-18190-exec-1] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| 查询Ceph压力折线图数据: CephPressureQueryDTO(clusterCode=null, startTime=Tue Jul 29 14:30:00 CST 2025, endTime=Tue Jul 29 15:30:00 CST 2025)
[] paas-center-traffic-info 2025-07-29 14:59:41.204 [http-nio-18190-exec-1] INFO  net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 从分钟聚合表查询数据: clusterCode=null, dataCount=0
[] paas-center-traffic-info 2025-07-29 14:59:41.249 [http-nio-18190-exec-1] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| 查询到5条Ceph压力数据
[] paas-center-traffic-info 2025-07-29 15:01:28.138 [Thread-9] WARN  com.alibaba.nacos.common.notify.NotifyCenter -| [NotifyCenter] Start destroying Publisher
[] paas-center-traffic-info 2025-07-29 15:01:28.139 [Thread-4] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder -| [HttpClientBeanHolder] Start destroying common HttpClient
[] paas-center-traffic-info 2025-07-29 15:01:28.139 [Thread-9] WARN  com.alibaba.nacos.common.notify.NotifyCenter -| [NotifyCenter] Destruction of the end
[] paas-center-traffic-info 2025-07-29 15:01:28.139 [Thread-4] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder -| [HttpClientBeanHolder] Destruction of the end
[] paas-center-traffic-info 2025-07-29 15:01:28.292 [SpringApplicationShutdownHook] INFO  com.alibaba.cloud.nacos.registry.NacosServiceRegistry -| De-registering from Nacos Server now...
[] paas-center-traffic-info 2025-07-29 15:01:28.296 [SpringApplicationShutdownHook] INFO  com.alibaba.cloud.nacos.registry.NacosServiceRegistry -| De-registration finished.
[] paas-center-traffic-info 2025-07-29 15:01:28.470 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoSendMsgService -| 关闭缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 15:01:31.618 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Shutdown initiated...
[] paas-center-traffic-info 2025-07-29 15:01:31.633 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Shutdown completed.
[] paas-center-traffic-info 2025-07-29 15:01:31.633 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoClickHouseService -| 关闭ClickHouse磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 15:01:31.633 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Shutdown initiated...
[] paas-center-traffic-info 2025-07-29 15:01:31.640 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Shutdown completed.
[] paas-center-traffic-info 2025-07-29 15:01:31.641 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.impl.DiskInfoServiceImpl -| 关闭ClickHouse磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 15:01:31.641 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.impl.PadTrafficInfoServiceImpl -| 关闭磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 15:01:39.372 [background-preinit] INFO  org.hibernate.validator.internal.util.Version -| HV000001: Hibernate Validator 6.2.5.Final
[] paas-center-traffic-info 2025-07-29 15:01:39.399 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| Starting PaasCenterTrafficInfoApplication using Java 1.8.0_252 on DESKTOP-D51FIJ4 with PID 46040 (D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes started by xskj in D:\dev\workspace\paas-center-traffic-info)
[] paas-center-traffic-info 2025-07-29 15:01:39.400 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| The following 1 profile is active: "docker"
[] paas-center-traffic-info 2025-07-29 15:01:39.464 [main] INFO  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader -| [Nacos Config] Load config[dataId=paas-center-traffic-info-docker.yaml, group=armcloud-paas-docker] success
[] paas-center-traffic-info 2025-07-29 15:01:40.628 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Multiple Spring Data modules found, entering strict repository configuration mode
[] paas-center-traffic-info 2025-07-29 15:01:40.631 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[] paas-center-traffic-info 2025-07-29 15:01:40.657 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
[] paas-center-traffic-info 2025-07-29 15:01:40.947 [main] INFO  org.springframework.cloud.context.scope.GenericScope -| BeanFactory id=96fb6da4-8676-337b-a389-f874aef18e94
[] paas-center-traffic-info 2025-07-29 15:01:41.219 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$EnhancerBySpringCGLIB$$d5d0c543] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:01:41.222 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$EnhancerBySpringCGLIB$$7e96cea7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:01:41.347 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:01:41.354 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:01:41.355 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:01:41.356 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$537/5930625] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:01:41.357 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:01:41.364 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:01:41.375 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:01:41.714 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer -| Tomcat initialized with port(s): 18190 (http)
[] paas-center-traffic-info 2025-07-29 15:01:41.727 [main] INFO  org.apache.coyote.http11.Http11NioProtocol -| Initializing ProtocolHandler ["http-nio-18190"]
[] paas-center-traffic-info 2025-07-29 15:01:41.728 [main] INFO  org.apache.catalina.core.StandardService -| Starting service [Tomcat]
[] paas-center-traffic-info 2025-07-29 15:01:41.728 [main] INFO  org.apache.catalina.core.StandardEngine -| Starting Servlet engine: [Apache Tomcat/9.0.68]
[] paas-center-traffic-info 2025-07-29 15:01:41.895 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] -| Initializing Spring embedded WebApplicationContext
[] paas-center-traffic-info 2025-07-29 15:01:41.896 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext -| Root WebApplicationContext: initialization completed in 2428 ms
[] paas-center-traffic-info 2025-07-29 15:01:42.219 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 初始化ClickHouse数据源...
[] paas-center-traffic-info 2025-07-29 15:01:42.219 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 成功加载 ClickHouse 驱动类
[] paas-center-traffic-info 2025-07-29 15:01:42.219 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| ClickHouse DataSource bean created successfully
[] paas-center-traffic-info 2025-07-29 15:01:42.224 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 创建ClickHouse SQLSessionFactory, 数据源类型: com.zaxxer.hikari.HikariDataSource
[] paas-center-traffic-info 2025-07-29 15:01:42.440 [main] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoClickHouseService -| 初始化ClickHouse磁盘信息缓冲区, batchSize=100, flushInterval=10s, capacity=10000
[] paas-center-traffic-info 2025-07-29 15:01:42.477 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Property 'configuration' or 'configLocation' not specified, using default MyBatis Configuration
[] paas-center-traffic-info 2025-07-29 15:01:42.612 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\CephPressureDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 15:01:42.630 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\NetStoragePadUnitDetailMapper.xml]'
[] paas-center-traffic-info 2025-07-29 15:01:42.741 [main] DEBUG com.baomidou.mybatisplus.core.toolkit.Sequence -| Initialization Sequence datacenterId:0 workerId:26
[] paas-center-traffic-info 2025-07-29 15:01:43.171 [main] INFO  org.redisson.Version -| Redisson 3.17.2
[] paas-center-traffic-info 2025-07-29 15:01:43.892 [redisson-netty-2-8] INFO  org.redisson.connection.pool.MasterPubSubConnectionPool -| 1 connections initialized for **************/**************:6379
[] paas-center-traffic-info 2025-07-29 15:01:43.949 [redisson-netty-2-19] INFO  org.redisson.connection.pool.MasterConnectionPool -| 24 connections initialized for **************/**************:6379
[] paas-center-traffic-info 2025-07-29 15:01:44.214 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Property 'configuration' or 'configLocation' not specified, using default MyBatis Configuration
[] paas-center-traffic-info 2025-07-29 15:01:44.222 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\traffic\DeviceSystemConfigDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 15:01:44.225 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\traffic\PadSystemConfigDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 15:01:44.227 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\traffic\PadTrafficInfoMapper.xml]'
[] paas-center-traffic-info 2025-07-29 15:01:44.354 [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean -| For 'paas-center-core' URL not provided. Will try picking an instance via load-balancing.
[] paas-center-traffic-info 2025-07-29 15:01:44.513 [main] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Starting...
[] paas-center-traffic-info 2025-07-29 15:01:44.819 [main] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Start completed.
[] paas-center-traffic-info 2025-07-29 15:01:45.031 [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean -| For 'paas-center-core' URL not provided. Will try picking an instance via load-balancing.
[] paas-center-traffic-info 2025-07-29 15:01:45.904 [main] INFO  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration -| a producer (test_topic_producer_ali_sdk) init on namesrv **************:9876
[] paas-center-traffic-info 2025-07-29 15:01:47.218 [main] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingProducer -| 》》》》  Initialized loading RocketMQ producer Success 》》》》
[] paas-center-traffic-info 2025-07-29 15:01:48.906 [main] INFO  org.springframework.cloud.commons.util.InetUtils -| Cannot determine local hostname
[] paas-center-traffic-info 2025-07-29 15:01:51.218 [main] INFO  org.springframework.cloud.commons.util.InetUtils -| Cannot determine local hostname
[] paas-center-traffic-info 2025-07-29 15:01:51.350 [main] WARN  org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger -| Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[] paas-center-traffic-info 2025-07-29 15:01:51.360 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver -| Exposing 2 endpoint(s) beneath base path '/actuator'
[] paas-center-traffic-info 2025-07-29 15:01:51.450 [main] INFO  org.apache.coyote.http11.Http11NioProtocol -| Starting ProtocolHandler ["http-nio-18190"]
[] paas-center-traffic-info 2025-07-29 15:01:51.474 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer -| Tomcat started on port(s): 18190 (http) with context path ''
[] paas-center-traffic-info 2025-07-29 15:01:51.516 [main] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[] paas-center-traffic-info 2025-07-29 15:01:51.516 [main] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[] paas-center-traffic-info 2025-07-29 15:01:51.666 [main] INFO  com.alibaba.cloud.nacos.registry.NacosServiceRegistry -| nacos registry, armcloud-paas-docker paas-center-traffic-info *************:18190 register finished
[] paas-center-traffic-info 2025-07-29 15:01:52.827 [main] INFO  org.springframework.cloud.commons.util.InetUtils -| Cannot determine local hostname
[] paas-center-traffic-info 2025-07-29 15:01:52.846 [main] INFO  net.armcloud.paascenter.traffic.info.service.impl.DiskInfoServiceImpl -| 初始化ClickHouse磁盘信息缓冲区, batchSize=1000, flushInterval=2s, capacity=10000
[] paas-center-traffic-info 2025-07-29 15:01:52.923 [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean -| For 'paas-center-core' URL not provided. Will try picking an instance via load-balancing.
[] paas-center-traffic-info 2025-07-29 15:01:52.934 [main] INFO  net.armcloud.paascenter.traffic.info.service.impl.PadTrafficInfoServiceImpl -| 初始化磁盘信息缓冲区, batchSize=10, flushInterval=5s, capacity=1000
[] paas-center-traffic-info 2025-07-29 15:01:52.963 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| Started PaasCenterTrafficInfoApplication in 17.119 seconds (JVM running for 18.08)
[] paas-center-traffic-info 2025-07-29 15:01:52.978 [main] DEBUG com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner -|   ...  DDL start create  ...  
[] paas-center-traffic-info 2025-07-29 15:01:52.979 [main] DEBUG com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner -|   ...  DDL end create  ...  
[] paas-center-traffic-info 2025-07-29 15:01:52.980 [main] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initializing rocketmq consumer...
[] paas-center-traffic-info 2025-07-29 15:01:52.981 [rocketMqConsumer-1-t-2] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic device_system_config_data 
[] paas-center-traffic-info 2025-07-29 15:01:52.982 [rocketMqConsumer-1-t-1] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic cbs_traffic_disk_data 
[] paas-center-traffic-info 2025-07-29 15:01:52.981 [rocketMqConsumer-1-t-5] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic pad_traffic_info_data 
[] paas-center-traffic-info 2025-07-29 15:01:52.981 [rocketMqConsumer-1-t-3] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic pad_system_config_data 
[] paas-center-traffic-info 2025-07-29 15:01:52.982 [rocketMqConsumer-1-t-4] INFO  net.armcloud.paascenter.traffic.info.rocketmq.configure.InitialLoadingConsumer -| start initialize RocketMQ listener for topic pad_traffic_data 
[] paas-center-traffic-info 2025-07-29 15:01:53.087 [main] INFO  com.alibaba.cloud.nacos.refresh.NacosContextRefresher -| [Nacos Config] Listening config: dataId=paas-center-traffic-info-docker.yaml, group=armcloud-paas-docker
[] paas-center-traffic-info 2025-07-29 15:01:53.522 [RMI TCP Connection(12)-************] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-2 - Starting...
[] paas-center-traffic-info 2025-07-29 15:01:53.654 [RMI TCP Connection(13)-************] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
[] paas-center-traffic-info 2025-07-29 15:01:53.655 [RMI TCP Connection(13)-************] INFO  org.springframework.web.servlet.DispatcherServlet -| Initializing Servlet 'dispatcherServlet'
[] paas-center-traffic-info 2025-07-29 15:01:53.668 [RMI TCP Connection(13)-************] INFO  org.springframework.web.servlet.DispatcherServlet -| Completed initialization in 13 ms
[] paas-center-traffic-info 2025-07-29 15:01:56.881 [RMI TCP Connection(12)-************] ERROR com.zaxxer.hikari.pool.HikariPool -| HikariPool-2 - Exception during pool initialization.
java.sql.SQLException: Connection refused: connect, server ClickHouseNode [uri=http://localhost:8123/armcloud, options={distributed_connections_pool_size=20,compress=1,max_parallel_replicas=2,keep_alive_timeout=30000,data_transfer_timeout=120000,max_partitions_per_insert_block=100,load_balancing=roundrobin,socket_timeout=60000,connection_timeout=30000}]@439196806
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:85)
	at com.clickhouse.jdbc.SqlExceptionUtils.create(SqlExceptionUtils.java:31)
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:90)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:131)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:335)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:288)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:157)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:41)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:330)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.getProduct(DataSourceHealthIndicator.java:122)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doDataSourceHealthCheck(DataSourceHealthIndicator.java:105)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doHealthCheck(DataSourceHealthIndicator.java:100)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:122)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468)
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76)
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309)
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401)
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at sun.reflect.GeneratedMethodAccessor102.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357)
	at sun.rmi.transport.Transport$1.run(Transport.java:200)
	at sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1340)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1315)
	at com.clickhouse.client.http.HttpUrlConnectionImpl.post(HttpUrlConnectionImpl.java:225)
	at com.clickhouse.client.http.ClickHouseHttpClient.send(ClickHouseHttpClient.java:124)
	at com.clickhouse.client.AbstractClient.execute(AbstractClient.java:280)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.sendOnce(ClickHouseClientBuilder.java:282)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.send(ClickHouseClientBuilder.java:294)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.execute(ClickHouseClientBuilder.java:349)
	at com.clickhouse.client.ClickHouseClient.executeAndWait(ClickHouseClient.java:1056)
	at com.clickhouse.client.ClickHouseRequest.executeAndWait(ClickHouseRequest.java:2154)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:128)
	... 64 common frames omitted
[] paas-center-traffic-info 2025-07-29 15:01:56.886 [RMI TCP Connection(12)-************] WARN  org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator -| DataSource health check failed
org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException: Connection refused: connect, server ClickHouseNode [uri=http://localhost:8123/armcloud, options={distributed_connections_pool_size=20,compress=1,max_parallel_replicas=2,keep_alive_timeout=30000,data_transfer_timeout=120000,max_partitions_per_insert_block=100,load_balancing=roundrobin,socket_timeout=60000,connection_timeout=30000}]@439196806
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:83)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:330)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.getProduct(DataSourceHealthIndicator.java:122)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doDataSourceHealthCheck(DataSourceHealthIndicator.java:105)
	at org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator.doHealthCheck(DataSourceHealthIndicator.java:100)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:122)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468)
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76)
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309)
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401)
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at sun.reflect.GeneratedMethodAccessor102.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357)
	at sun.rmi.transport.Transport$1.run(Transport.java:200)
	at sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLException: Connection refused: connect, server ClickHouseNode [uri=http://localhost:8123/armcloud, options={distributed_connections_pool_size=20,compress=1,max_parallel_replicas=2,keep_alive_timeout=30000,data_transfer_timeout=120000,max_partitions_per_insert_block=100,load_balancing=roundrobin,socket_timeout=60000,connection_timeout=30000}]@439196806
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:85)
	at com.clickhouse.jdbc.SqlExceptionUtils.create(SqlExceptionUtils.java:31)
	at com.clickhouse.jdbc.SqlExceptionUtils.handle(SqlExceptionUtils.java:90)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:131)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:335)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:288)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:157)
	at com.clickhouse.jdbc.ClickHouseDriver.connect(ClickHouseDriver.java:41)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	... 50 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1340)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1315)
	at com.clickhouse.client.http.HttpUrlConnectionImpl.post(HttpUrlConnectionImpl.java:225)
	at com.clickhouse.client.http.ClickHouseHttpClient.send(ClickHouseHttpClient.java:124)
	at com.clickhouse.client.AbstractClient.execute(AbstractClient.java:280)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.sendOnce(ClickHouseClientBuilder.java:282)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.send(ClickHouseClientBuilder.java:294)
	at com.clickhouse.client.ClickHouseClientBuilder$Agent.execute(ClickHouseClientBuilder.java:349)
	at com.clickhouse.client.ClickHouseClient.executeAndWait(ClickHouseClient.java:1056)
	at com.clickhouse.client.ClickHouseRequest.executeAndWait(ClickHouseRequest.java:2154)
	at com.clickhouse.jdbc.internal.ClickHouseConnectionImpl.getServerInfo(ClickHouseConnectionImpl.java:128)
	... 64 common frames omitted
[] paas-center-traffic-info 2025-07-29 15:01:56.887 [RMI TCP Connection(12)-************] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Starting...
[] paas-center-traffic-info 2025-07-29 15:01:56.892 [RMI TCP Connection(12)-************] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Start completed.
[] paas-center-traffic-info 2025-07-29 15:02:47.149 [http-nio-18190-exec-1] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| 查询Ceph压力折线图数据: CephPressureQueryDTO(clusterCode=null, startTime=Tue Jul 29 14:30:00 CST 2025, endTime=Tue Jul 29 15:30:00 CST 2025)
[] paas-center-traffic-info 2025-07-29 15:02:47.192 [http-nio-18190-exec-1] WARN  com.zaxxer.hikari.pool.PoolBase -| HikariPool-3 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@45c25898 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
[] paas-center-traffic-info 2025-07-29 15:02:47.196 [http-nio-18190-exec-1] WARN  com.zaxxer.hikari.pool.PoolBase -| HikariPool-3 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7fe95fff (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
[] paas-center-traffic-info 2025-07-29 15:02:47.198 [http-nio-18190-exec-1] WARN  com.zaxxer.hikari.pool.PoolBase -| HikariPool-3 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3d3519e2 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
[] paas-center-traffic-info 2025-07-29 15:02:47.200 [http-nio-18190-exec-1] WARN  com.zaxxer.hikari.pool.PoolBase -| HikariPool-3 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5f0ff208 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
[] paas-center-traffic-info 2025-07-29 15:02:47.202 [http-nio-18190-exec-1] WARN  com.zaxxer.hikari.pool.PoolBase -| HikariPool-3 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@6aac58b1 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
[] paas-center-traffic-info 2025-07-29 15:02:47.204 [http-nio-18190-exec-1] WARN  com.zaxxer.hikari.pool.PoolBase -| HikariPool-3 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@48a5328 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
[] paas-center-traffic-info 2025-07-29 15:02:47.205 [http-nio-18190-exec-1] WARN  com.zaxxer.hikari.pool.PoolBase -| HikariPool-3 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@1165b052 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
[] paas-center-traffic-info 2025-07-29 15:02:47.208 [http-nio-18190-exec-1] WARN  com.zaxxer.hikari.pool.PoolBase -| HikariPool-3 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@315a8fc4 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
[] paas-center-traffic-info 2025-07-29 15:02:47.210 [http-nio-18190-exec-1] WARN  com.zaxxer.hikari.pool.PoolBase -| HikariPool-3 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@393b308e (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
[] paas-center-traffic-info 2025-07-29 15:02:47.213 [http-nio-18190-exec-1] WARN  com.zaxxer.hikari.pool.PoolBase -| HikariPool-3 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@6ffe3792 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
[] paas-center-traffic-info 2025-07-29 15:03:07.770 [http-nio-18190-exec-10] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| 查询Ceph压力折线图数据: CephPressureQueryDTO(clusterCode=null, startTime=Tue Jul 29 14:30:00 CST 2025, endTime=Tue Jul 29 15:30:00 CST 2025)
[] paas-center-traffic-info 2025-07-29 15:03:17.171 [http-nio-18190-exec-1] ERROR net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 查询Ceph压力折线图数据失败
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLTransientConnectionException: HikariPool-3 - Connection is not available, request timed out after 30002ms.
### The error may exist in file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\CephPressureDataMapper.xml]
### The error may involve net.armcloud.paascenter.traffic.info.mapper.paas.ClusterMonitorDataMapper.countMinuteDataByTimeRange
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLTransientConnectionException: HikariPool-3 - Connection is not available, request timed out after 30002ms.
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:97)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy115.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:87)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy123.countMinuteDataByTimeRange(Unknown Source)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl.queryFromMinuteTable(CephPressureDataServiceImpl.java:124)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl.getChartData(CephPressureDataServiceImpl.java:94)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl$$FastClassBySpringCGLIB$$c3d291d4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl$$EnhancerBySpringCGLIB$$13972334.getChartData(<generated>)
	at net.armcloud.paascenter.traffic.info.controller.TrafficDataController.getCephPressureChart(TrafficDataController.java:123)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.armcloud.paascenter.traffic.info.filter.RequestBodyCacheFilter.doFilter(RequestBodyCacheFilter.java:26)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLTransientConnectionException: HikariPool-3 - Connection is not available, request timed out after 30002ms.
### The error may exist in file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\CephPressureDataMapper.xml]
### The error may involve net.armcloud.paascenter.traffic.info.mapper.paas.ClusterMonitorDataMapper.countMinuteDataByTimeRange
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLTransientConnectionException: HikariPool-3 - Connection is not available, request timed out after 30002ms.
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:153)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 72 common frames omitted
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLTransientConnectionException: HikariPool-3 - Connection is not available, request timed out after 30002ms.
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:83)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:89)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	... 80 common frames omitted
Caused by: java.sql.SQLTransientConnectionException: HikariPool-3 - Connection is not available, request timed out after 30002ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:128)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	... 90 common frames omitted
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.access$100(HikariPool.java:71)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:726)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:712)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	... 1 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 15 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 18 common frames omitted
[] paas-center-traffic-info 2025-07-29 15:03:17.172 [http-nio-18190-exec-1] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| 查询到0条Ceph压力数据
[] paas-center-traffic-info 2025-07-29 15:03:37.773 [http-nio-18190-exec-10] ERROR net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl -| 查询Ceph压力折线图数据失败
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLTransientConnectionException: HikariPool-3 - Connection is not available, request timed out after 30001ms.
### The error may exist in file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\CephPressureDataMapper.xml]
### The error may involve net.armcloud.paascenter.traffic.info.mapper.paas.ClusterMonitorDataMapper.countMinuteDataByTimeRange
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLTransientConnectionException: HikariPool-3 - Connection is not available, request timed out after 30001ms.
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:97)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy115.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:87)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy123.countMinuteDataByTimeRange(Unknown Source)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl.queryFromMinuteTable(CephPressureDataServiceImpl.java:124)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl.getChartData(CephPressureDataServiceImpl.java:94)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl$$FastClassBySpringCGLIB$$c3d291d4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at net.armcloud.paascenter.traffic.info.service.impl.CephPressureDataServiceImpl$$EnhancerBySpringCGLIB$$13972334.getChartData(<generated>)
	at net.armcloud.paascenter.traffic.info.controller.TrafficDataController.getCephPressureChart(TrafficDataController.java:123)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.armcloud.paascenter.traffic.info.filter.RequestBodyCacheFilter.doFilter(RequestBodyCacheFilter.java:26)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLTransientConnectionException: HikariPool-3 - Connection is not available, request timed out after 30001ms.
### The error may exist in file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\CephPressureDataMapper.xml]
### The error may involve net.armcloud.paascenter.traffic.info.mapper.paas.ClusterMonitorDataMapper.countMinuteDataByTimeRange
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLTransientConnectionException: HikariPool-3 - Connection is not available, request timed out after 30001ms.
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:153)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 72 common frames omitted
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLTransientConnectionException: HikariPool-3 - Connection is not available, request timed out after 30001ms.
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:83)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:89)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	... 80 common frames omitted
Caused by: java.sql.SQLTransientConnectionException: HikariPool-3 - Connection is not available, request timed out after 30001ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:128)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	... 90 common frames omitted
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.access$100(HikariPool.java:71)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:726)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:712)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	... 1 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 15 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 18 common frames omitted
[] paas-center-traffic-info 2025-07-29 15:03:37.774 [http-nio-18190-exec-10] INFO  net.armcloud.paascenter.traffic.info.controller.TrafficDataController -| 查询到0条Ceph压力数据
[] paas-center-traffic-info 2025-07-29 15:11:55.257 [Thread-10] WARN  com.alibaba.nacos.common.notify.NotifyCenter -| [NotifyCenter] Start destroying Publisher
[] paas-center-traffic-info 2025-07-29 15:11:55.257 [Thread-4] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder -| [HttpClientBeanHolder] Start destroying common HttpClient
[] paas-center-traffic-info 2025-07-29 15:11:55.257 [Thread-10] WARN  com.alibaba.nacos.common.notify.NotifyCenter -| [NotifyCenter] Destruction of the end
[] paas-center-traffic-info 2025-07-29 15:11:55.257 [Thread-4] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder -| [HttpClientBeanHolder] Destruction of the end
[] paas-center-traffic-info 2025-07-29 15:11:55.431 [SpringApplicationShutdownHook] INFO  com.alibaba.cloud.nacos.registry.NacosServiceRegistry -| De-registering from Nacos Server now...
[] paas-center-traffic-info 2025-07-29 15:11:55.734 [SpringApplicationShutdownHook] ERROR com.alibaba.cloud.nacos.registry.NacosServiceRegistry -| ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='**************:8848', username='', password='', endpoint='', namespace='armcloud-paas-docker', watchDelay=30000, logName='', service='paas-center-traffic-info', weight=1.0, clusterName='DEFAULT', group='armcloud-paas-docker', namingLoadCacheAtStart='false', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='*************', networkInterface='', port=18190, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:639)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:619)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:356)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:233)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:219)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:125)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:201)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:191)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:107)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:249)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:264)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:201)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:197)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1106)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1075)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:172)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1021)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:145)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:114)
	at java.lang.Thread.run(Thread.java:748)
[] paas-center-traffic-info 2025-07-29 15:11:55.734 [SpringApplicationShutdownHook] INFO  com.alibaba.cloud.nacos.registry.NacosServiceRegistry -| De-registration finished.
[] paas-center-traffic-info 2025-07-29 15:11:55.764 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoSendMsgService -| 关闭缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 15:11:55.825 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Shutdown initiated...
[] paas-center-traffic-info 2025-07-29 15:12:25.827 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-3 - Shutdown completed.
[] paas-center-traffic-info 2025-07-29 15:12:25.827 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoClickHouseService -| 关闭ClickHouse磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 15:12:25.828 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Shutdown initiated...
[] paas-center-traffic-info 2025-07-29 15:12:25.837 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource -| HikariPool-1 - Shutdown completed.
[] paas-center-traffic-info 2025-07-29 15:12:25.838 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.impl.DiskInfoServiceImpl -| 关闭ClickHouse磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 15:12:25.838 [SpringApplicationShutdownHook] INFO  net.armcloud.paascenter.traffic.info.service.impl.PadTrafficInfoServiceImpl -| 关闭磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 15:12:41.097 [background-preinit] INFO  org.hibernate.validator.internal.util.Version -| HV000001: Hibernate Validator 6.2.5.Final
[] paas-center-traffic-info 2025-07-29 15:12:41.124 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| Starting PaasCenterTrafficInfoApplication using Java 1.8.0_252 on DESKTOP-D51FIJ4 with PID 46044 (D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes started by xskj in D:\dev\workspace\paas-center-traffic-info)
[] paas-center-traffic-info 2025-07-29 15:12:41.126 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| The following 1 profile is active: "docker"
[] paas-center-traffic-info 2025-07-29 15:12:41.194 [main] INFO  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader -| [Nacos Config] Load config[dataId=paas-center-traffic-info-docker.yaml, group=armcloud-paas-docker] success
[] paas-center-traffic-info 2025-07-29 15:12:42.131 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Multiple Spring Data modules found, entering strict repository configuration mode
[] paas-center-traffic-info 2025-07-29 15:12:42.135 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[] paas-center-traffic-info 2025-07-29 15:12:42.162 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
[] paas-center-traffic-info 2025-07-29 15:12:42.430 [main] INFO  org.springframework.cloud.context.scope.GenericScope -| BeanFactory id=96fb6da4-8676-337b-a389-f874aef18e94
[] paas-center-traffic-info 2025-07-29 15:12:42.731 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$EnhancerBySpringCGLIB$$8faf4b55] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:12:42.734 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$EnhancerBySpringCGLIB$$387554b9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:12:42.856 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:12:42.864 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:12:42.866 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:12:42.866 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$537/449951543] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:12:42.867 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:12:42.874 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:12:42.878 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:12:43.206 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer -| Tomcat initialized with port(s): 18190 (http)
[] paas-center-traffic-info 2025-07-29 15:12:43.218 [main] INFO  org.apache.coyote.http11.Http11NioProtocol -| Initializing ProtocolHandler ["http-nio-18190"]
[] paas-center-traffic-info 2025-07-29 15:12:43.218 [main] INFO  org.apache.catalina.core.StandardService -| Starting service [Tomcat]
[] paas-center-traffic-info 2025-07-29 15:12:43.218 [main] INFO  org.apache.catalina.core.StandardEngine -| Starting Servlet engine: [Apache Tomcat/9.0.68]
[] paas-center-traffic-info 2025-07-29 15:12:43.387 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] -| Initializing Spring embedded WebApplicationContext
[] paas-center-traffic-info 2025-07-29 15:12:43.387 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext -| Root WebApplicationContext: initialization completed in 2189 ms
[] paas-center-traffic-info 2025-07-29 15:12:43.668 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 初始化ClickHouse数据源...
[] paas-center-traffic-info 2025-07-29 15:12:43.669 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 成功加载 ClickHouse 驱动类
[] paas-center-traffic-info 2025-07-29 15:12:43.669 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| ClickHouse DataSource bean created successfully
[] paas-center-traffic-info 2025-07-29 15:12:43.672 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 创建ClickHouse SQLSessionFactory, 数据源类型: com.zaxxer.hikari.HikariDataSource
[] paas-center-traffic-info 2025-07-29 15:12:43.866 [main] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoClickHouseService -| 初始化ClickHouse磁盘信息缓冲区, batchSize=100, flushInterval=10s, capacity=10000
[] paas-center-traffic-info 2025-07-29 15:12:43.890 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Property 'configuration' or 'configLocation' not specified, using default MyBatis Configuration
[] paas-center-traffic-info 2025-07-29 15:12:44.086 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\CephPressureDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 15:12:44.123 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\NetStoragePadUnitDetailMapper.xml]'
[] paas-center-traffic-info 2025-07-29 15:12:44.253 [main] DEBUG com.baomidou.mybatisplus.core.toolkit.Sequence -| Initialization Sequence datacenterId:0 workerId:30
[] paas-center-traffic-info 2025-07-29 15:12:44.711 [main] INFO  org.redisson.Version -| Redisson 3.17.2
[] paas-center-traffic-info 2025-07-29 15:12:55.618 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext -| Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonDistributedLock' defined in file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\net\armcloud\paascenter\traffic\info\redis\lock\RedissonDistributedLock.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: **************/**************:6379
[] paas-center-traffic-info 2025-07-29 15:12:55.619 [main] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoClickHouseService -| 关闭ClickHouse磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 15:12:55.623 [main] INFO  org.apache.catalina.core.StandardService -| Stopping service [Tomcat]
[] paas-center-traffic-info 2025-07-29 15:12:55.639 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener -| 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
[] paas-center-traffic-info 2025-07-29 15:12:55.680 [main] ERROR org.springframework.boot.SpringApplication -| Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonDistributedLock' defined in file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\net\armcloud\paascenter\traffic\info\redis\lock\RedissonDistributedLock.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: **************/**************:6379
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:800)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:229)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:745)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:420)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1317)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication.main(PaasCenterTrafficInfoApplication.java:17)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: **************/**************:6379
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:658)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:486)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 19 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: **************/**************:6379
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 33 common frames omitted
Caused by: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: **************/**************:6379
	at org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:150)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:774)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:750)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:488)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1990)
	at org.redisson.connection.pool.ConnectionPool.promiseFailure(ConnectionPool.java:307)
	at org.redisson.connection.pool.ConnectionPool.lambda$createConnection$6(ConnectionPool.java:273)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:774)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:750)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:488)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1990)
	at org.redisson.client.RedisClient$1$2.run(RedisClient.java:235)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:174)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:167)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.util.concurrent.CompletionException: io.netty.channel.ConnectTimeoutException: connection timed out: **************/**************:6379
	at java.util.concurrent.CompletableFuture.encodeRelay(CompletableFuture.java:326)
	at java.util.concurrent.CompletableFuture.completeRelay(CompletableFuture.java:338)
	at java.util.concurrent.CompletableFuture.uniRelay(CompletableFuture.java:925)
	at java.util.concurrent.CompletableFuture$UniRelay.tryFire(CompletableFuture.java:913)
	... 12 common frames omitted
Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: **************/**************:6379
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:153)
	... 9 common frames omitted
[] paas-center-traffic-info 2025-07-29 15:12:56.613 [Thread-4] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder -| [HttpClientBeanHolder] Start destroying common HttpClient
[] paas-center-traffic-info 2025-07-29 15:12:56.616 [Thread-9] WARN  com.alibaba.nacos.common.notify.NotifyCenter -| [NotifyCenter] Start destroying Publisher
[] paas-center-traffic-info 2025-07-29 15:12:56.618 [Thread-9] WARN  com.alibaba.nacos.common.notify.NotifyCenter -| [NotifyCenter] Destruction of the end
[] paas-center-traffic-info 2025-07-29 15:12:56.618 [Thread-4] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder -| [HttpClientBeanHolder] Destruction of the end
[] paas-center-traffic-info 2025-07-29 15:41:58.399 [background-preinit] INFO  org.hibernate.validator.internal.util.Version -| HV000001: Hibernate Validator 6.2.5.Final
[] paas-center-traffic-info 2025-07-29 15:41:58.439 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| Starting PaasCenterTrafficInfoApplication using Java 1.8.0_252 on DESKTOP-D51FIJ4 with PID 14572 (D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes started by xskj in D:\dev\workspace\paas-center-traffic-info)
[] paas-center-traffic-info 2025-07-29 15:41:58.441 [main] INFO  net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication -| The following 1 profile is active: "docker"
[] paas-center-traffic-info 2025-07-29 15:41:58.516 [main] INFO  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader -| [Nacos Config] Load config[dataId=paas-center-traffic-info-docker.yaml, group=armcloud-paas-docker] success
[] paas-center-traffic-info 2025-07-29 15:41:59.666 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Multiple Spring Data modules found, entering strict repository configuration mode
[] paas-center-traffic-info 2025-07-29 15:41:59.669 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[] paas-center-traffic-info 2025-07-29 15:41:59.695 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate -| Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
[] paas-center-traffic-info 2025-07-29 15:41:59.991 [main] INFO  org.springframework.cloud.context.scope.GenericScope -| BeanFactory id=2a44cdde-6a4d-3403-b7e5-e7ab5df94e22
[] paas-center-traffic-info 2025-07-29 15:42:00.273 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$EnhancerBySpringCGLIB$$7b5dd0dd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:42:00.277 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$EnhancerBySpringCGLIB$$2423da41] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:42:00.415 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:42:00.423 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:42:00.424 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:42:00.424 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$537/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:42:00.425 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:42:00.432 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:42:00.439 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -| Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[] paas-center-traffic-info 2025-07-29 15:42:00.766 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer -| Tomcat initialized with port(s): 18190 (http)
[] paas-center-traffic-info 2025-07-29 15:42:00.779 [main] INFO  org.apache.coyote.http11.Http11NioProtocol -| Initializing ProtocolHandler ["http-nio-18190"]
[] paas-center-traffic-info 2025-07-29 15:42:00.780 [main] INFO  org.apache.catalina.core.StandardService -| Starting service [Tomcat]
[] paas-center-traffic-info 2025-07-29 15:42:00.780 [main] INFO  org.apache.catalina.core.StandardEngine -| Starting Servlet engine: [Apache Tomcat/9.0.68]
[] paas-center-traffic-info 2025-07-29 15:42:00.978 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] -| Initializing Spring embedded WebApplicationContext
[] paas-center-traffic-info 2025-07-29 15:42:00.978 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext -| Root WebApplicationContext: initialization completed in 2449 ms
[] paas-center-traffic-info 2025-07-29 15:42:01.296 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 初始化ClickHouse数据源...
[] paas-center-traffic-info 2025-07-29 15:42:01.296 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 成功加载 ClickHouse 驱动类
[] paas-center-traffic-info 2025-07-29 15:42:01.296 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| ClickHouse DataSource bean created successfully
[] paas-center-traffic-info 2025-07-29 15:42:01.300 [main] INFO  net.armcloud.paascenter.traffic.info.config.datasource.ClickHouseDatasourceConfig -| 创建ClickHouse SQLSessionFactory, 数据源类型: com.zaxxer.hikari.HikariDataSource
[] paas-center-traffic-info 2025-07-29 15:42:01.529 [main] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoClickHouseService -| 初始化ClickHouse磁盘信息缓冲区, batchSize=100, flushInterval=10s, capacity=10000
[] paas-center-traffic-info 2025-07-29 15:42:01.550 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Property 'configuration' or 'configLocation' not specified, using default MyBatis Configuration
[] paas-center-traffic-info 2025-07-29 15:42:01.683 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\CephPressureDataMapper.xml]'
[] paas-center-traffic-info 2025-07-29 15:42:01.703 [main] DEBUG com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean -| Parsed mapper file: 'file [D:\dev\workspace\paas-center-traffic-info\paas-center-traffic-info\target\classes\mapper\paas\NetStoragePadUnitDetailMapper.xml]'
[] paas-center-traffic-info 2025-07-29 15:42:01.817 [main] DEBUG com.baomidou.mybatisplus.core.toolkit.Sequence -| Initialization Sequence datacenterId:0 workerId:29
[] paas-center-traffic-info 2025-07-29 15:42:02.263 [main] INFO  org.redisson.Version -| Redisson 3.17.2
[] paas-center-traffic-info 2025-07-29 15:42:13.019 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext -| Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'trafficDataController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'cephPressureDataServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redisService': Unsatisfied dependency expressed through field 'redisTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redisTemplate' defined in class path resource [net/armcloud/paascenter/traffic/info/redis/configure/RedisConfig.class]: Unsatisfied dependency expressed through method 'redisTemplate' parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: **************/**************:6379
[] paas-center-traffic-info 2025-07-29 15:42:13.019 [main] INFO  net.armcloud.paascenter.traffic.info.service.PadTrafficInfoClickHouseService -| 关闭ClickHouse磁盘信息缓冲区，最后一次刷新
[] paas-center-traffic-info 2025-07-29 15:42:13.023 [main] INFO  org.apache.catalina.core.StandardService -| Stopping service [Tomcat]
[] paas-center-traffic-info 2025-07-29 15:42:13.036 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener -| 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
[] paas-center-traffic-info 2025-07-29 15:42:13.069 [main] ERROR org.springframework.boot.SpringApplication -| Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'trafficDataController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'cephPressureDataServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redisService': Unsatisfied dependency expressed through field 'redisTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redisTemplate' defined in class path resource [net/armcloud/paascenter/traffic/info/redis/configure/RedisConfig.class]: Unsatisfied dependency expressed through method 'redisTemplate' parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: **************/**************:6379
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:332)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:745)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:420)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1317)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at net.armcloud.paascenter.traffic.info.PaasCenterTrafficInfoApplication.main(PaasCenterTrafficInfoApplication.java:17)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'cephPressureDataServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redisService': Unsatisfied dependency expressed through field 'redisTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redisTemplate' defined in class path resource [net/armcloud/paascenter/traffic/info/redis/configure/RedisConfig.class]: Unsatisfied dependency expressed through method 'redisTemplate' parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: **************/**************:6379
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:332)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:544)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:520)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:673)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:329)
	... 17 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redisService': Unsatisfied dependency expressed through field 'redisTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redisTemplate' defined in class path resource [net/armcloud/paascenter/traffic/info/redis/configure/RedisConfig.class]: Unsatisfied dependency expressed through method 'redisTemplate' parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: **************/**************:6379
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:479)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:550)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:520)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:673)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:329)
	... 33 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redisTemplate' defined in class path resource [net/armcloud/paascenter/traffic/info/redis/configure/RedisConfig.class]: Unsatisfied dependency expressed through method 'redisTemplate' parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: **************/**************:6379
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:800)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	... 50 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: **************/**************:6379
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:800)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 63 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: **************/**************:6379
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:658)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:486)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 77 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: **************/**************:6379
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 91 common frames omitted
Caused by: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: **************/**************:6379
	at org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:150)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:774)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:750)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:488)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1990)
	at org.redisson.connection.pool.ConnectionPool.promiseFailure(ConnectionPool.java:307)
	at org.redisson.connection.pool.ConnectionPool.lambda$createConnection$6(ConnectionPool.java:273)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:774)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:750)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:488)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1990)
	at org.redisson.client.RedisClient$1$2.run(RedisClient.java:235)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:174)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:167)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.util.concurrent.CompletionException: io.netty.channel.ConnectTimeoutException: connection timed out: **************/**************:6379
	at java.util.concurrent.CompletableFuture.encodeRelay(CompletableFuture.java:326)
	at java.util.concurrent.CompletableFuture.completeRelay(CompletableFuture.java:338)
	at java.util.concurrent.CompletableFuture.uniRelay(CompletableFuture.java:925)
	at java.util.concurrent.CompletableFuture$UniRelay.tryFire(CompletableFuture.java:913)
	... 12 common frames omitted
Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: **************/**************:6379
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:153)
	... 9 common frames omitted
[] paas-center-traffic-info 2025-07-29 15:42:13.997 [Thread-10] WARN  com.alibaba.nacos.common.notify.NotifyCenter -| [NotifyCenter] Start destroying Publisher
[] paas-center-traffic-info 2025-07-29 15:42:13.998 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder -| [HttpClientBeanHolder] Start destroying common HttpClient
[] paas-center-traffic-info 2025-07-29 15:42:13.998 [Thread-10] WARN  com.alibaba.nacos.common.notify.NotifyCenter -| [NotifyCenter] Destruction of the end
